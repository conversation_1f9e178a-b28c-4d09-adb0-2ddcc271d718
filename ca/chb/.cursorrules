# Cursor Rules for Professional Financial Literacy Website

## Project Overview
This repository contains multiple versions of a professional financial literacy and fraud protection website for Canadian Financial Security Group Inc. Each folder represents a different implementation approach.

## Project Structure
- **Folder 1**: Ukrainian version (legacy, do not modify)
- **Folder 2**: English version with basic HTML/CSS
- **Folder 3**: Complete professional website with advanced features ⭐ **MAIN VERSION**
- **Folder 4**: Wealth Management variant (premium gold theme)
- **Folder 5**: Financial Education variant (green education theme)
- **Folder 7**: Digital Security variant (tech blue/red theme)
- **Root files**: Configuration and documentation

## Design Guidelines

### Language & Content
- **Language**: All content must be in English
- **Tone**: Professional, business-like, authoritative
- **Target Audience**: Canadian residents seeking financial literacy and fraud protection
- **Content Quality**: Educational, accurate, Canadian-specific information

### Visual Design Standards
- **Style**: Professional business website, similar to official financial institutions
- **Color Scheme**: Corporate colors - blues, greens, whites with professional gradients
- **Typography**: Clean, readable fonts (Inter, Helvetica, Arial)
- **Images**: Use actual professional photos instead of emoji/icons where possible
- **Layout**: Clean, organized, government/bank website aesthetic

### Website Variants

#### Main Implementation (Folder 3)
- **Technology**: Pure HTML, CSS, and JavaScript
- **Theme**: General Financial Security (blue/gold theme)
- **Focus**: Comprehensive financial literacy and fraud protection
- **Target**: General Canadian population

#### Wealth Management Variant (Folder 4)
- **Brand**: Canadian Wealth Management (CWM)
- **Theme**: Premium wealth management (dark/gold theme)
- **Focus**: High-net-worth individuals, investment advisory
- **Services**: Private wealth advisory, portfolio management, alternative investments

#### Financial Education Variant (Folder 5)
- **Brand**: Canadian Financial Education (CFE)
- **Theme**: Educational platform (blue/green theme)
- **Focus**: Learning and training programs
- **Services**: Interactive workshops, online learning, personal coaching

#### Digital Security Variant (Folder 7)
- **Brand**: Canadian Digital Security (CDS)
- **Theme**: Cybersecurity focused (dark blue/red theme)
- **Focus**: Digital protection and cybersecurity
- **Services**: Security audits, threat monitoring, incident response

## Development Guidelines

### Code Quality Standards
- Write clean, semantic HTML5 markup
- Use modern CSS features (Grid, Flexbox, Custom Properties)
- Implement vanilla JavaScript for interactivity
- Follow accessibility best practices (WCAG 2.1 AA)
- Optimize for performance and Core Web Vitals
- Use progressive enhancement principles

### File Organization
- Maintain clear folder structure for each implementation
- Separate concerns: HTML structure, CSS styling, JS functionality
- Use descriptive file names and consistent naming conventions
- Keep assets organized (images, fonts, icons)
- Document any external dependencies

## Content Requirements

### Mandatory Pages
1. **Homepage** - Hero, statistics, services overview, testimonials
2. **About Us** - Mission, team, credentials, certifications
3. **Financial Literacy** - Educational content, guides, best practices
4. **Fraud Protection** - Scam types, prevention, reporting procedures
5. **Resources** - Calculators, tools, official links, downloads
6. **Contact** - Form, office locations, hours, emergency contacts
7. **Privacy Policy** - PIPEDA compliant, Canadian privacy laws

### Interactive Features
- Budget Calculator with real-time calculations
- Mortgage Calculator with Canadian rates
- Retirement Savings Calculator with CPP/OAS integration
- Contact Form with proper validation
- FAQ sections with smooth animations
- Mobile navigation menu
- Newsletter signup

### Business Information
- **Company**: Canadian Financial Security Group Inc.
- **Vancouver Office**: 2580 Granville Street, Suite 1200, Vancouver, BC V6H 4B1
- **Toronto Office**: 145 King Street West, Suite 2600, Toronto, ON M5H 1J8
- **Main Phone**: +****************
- **Toll-Free**: +1 (800) 555-CFSG (2374)
- **Email**: <EMAIL>
- **Website**: www.canadianfinancialsg.com

### Service Pricing (CAD)
- Initial Financial Assessment: $299
- Comprehensive Financial Plan: $899
- Fraud Protection Audit: $199
- Monthly Advisory Retainer: $149/month
- Corporate Training Programs: Starting at $2,500
- Emergency Fraud Response: $399

### Design System
- **Primary Colors**: Navy Blue (#1B365D), Gold Accent (#C9A961)
- **Secondary Colors**: White (#FFFFFF), Light Gray (#F8F9FA)
- **Typography**: Montserrat (headings), Inter (body text)
- **Style**: Professional, trustworthy, modern corporate design

## File Organization

### Next.js Project Structure
```
/
├── package.json
├── next.config.js
├── tailwind.config.js
├── tsconfig.json
├── app/
│   ├── layout.tsx
│   ├── page.tsx (Homepage)
│   ├── about/page.tsx
│   ├── financial-literacy/page.tsx
│   ├── fraud-protection/page.tsx
│   ├── resources/page.tsx
│   ├── contact/page.tsx
│   ├── privacy/page.tsx
│   └── globals.css
├── components/
│   ├── ui/
│   ├── layout/
│   ├── forms/
│   └── calculators/
├── lib/
│   ├── utils.ts
│   ├── unsplash.ts
│   └── types.ts
├── public/
│   ├── images/
│   ├── icons/
│   └── favicon.ico
└── README.md
```

### Naming Conventions
- **Files**: kebab-case (financial-literacy.html)
- **CSS Classes**: BEM methodology or semantic naming
- **IDs**: descriptive camelCase
- **Images**: descriptive names with dimensions (team-photo-400x300.jpg)

## Development Guidelines

### HTML Standards
- Semantic HTML5 elements
- Proper heading hierarchy (h1 → h6)
- Valid markup (W3C compliant)
- Alt text for all images
- Proper form labels and validation

### CSS Standards
- Mobile-first responsive design
- CSS Grid and Flexbox for layouts
- CSS Custom Properties (variables) for consistency
- Organized stylesheets (base, components, pages)
- Performance optimized (minimize unused CSS)

### JavaScript Standards
- Vanilla JS (no frameworks unless absolutely necessary)
- Progressive enhancement
- Proper error handling
- Accessible interactions
- Performance optimized

## Image Requirements

### Professional Photos
- **Team Photos**: Professional headshots, business attire
- **Hero Images**: High-quality financial/business stock photos
- **Content Images**: Relevant photos instead of icons/emojis
- **Format**: WebP with JPG fallback for compatibility
- **Optimization**: Compressed for web, multiple sizes for responsive

### Placeholder Sources
- Use high-quality stock photo services
- Ensure proper licensing for commercial use
- Maintain consistent photography style
- Professional business aesthetic

## Content Guidelines

### Writing Style
- **Professional**: Business formal tone
- **Clear**: Easy to understand financial concepts
- **Authoritative**: Expert knowledge demonstration
- **Helpful**: Actionable advice and guidance
- **Canadian**: Local references and regulations

### Financial Content
- Accurate Canadian financial information
- Current interest rates and regulations
- Proper disclaimers where required
- Educational focus, not financial advice
- References to official Canadian sources

## Testing Requirements

### Functionality Testing
- All calculators work correctly
- Forms submit and validate properly
- Mobile navigation functions
- All links work correctly
- Cross-browser compatibility

### Performance Testing
- Page load speed < 3 seconds
- Mobile performance optimization
- Image optimization verification
- JavaScript performance

### Accessibility Testing
- Keyboard navigation works
- Screen reader compatibility
- Color contrast compliance
- Alt text for all images

## Deployment Considerations

### Production Ready
- Minified CSS/JS
- Optimized images
- Proper caching headers
- SSL certificate required
- Error pages (404) configured

### Analytics & Monitoring
- Google Analytics integration ready
- Contact form tracking
- Performance monitoring setup
- User behavior tracking

## Quality Assurance

### Code Review Checklist
- [ ] English content throughout
- [ ] Professional business design
- [ ] All interactive features work
- [ ] Mobile responsive
- [ ] Canadian compliance
- [ ] SEO optimized
- [ ] Performance optimized
- [ ] Accessibility compliant

### Content Review
- [ ] All text in proper English
- [ ] Canadian financial information accurate
- [ ] Professional tone maintained
- [ ] No placeholder content remaining
- [ ] Contact information realistic

## Maintenance Guidelines

### Regular Updates
- Keep financial information current
- Update contact information as needed
- Refresh team photos annually
- Review and update privacy policy
- Monitor and fix broken links

### Security Considerations
- Regular security updates
- Form spam protection
- HTTPS enforcement
- Data protection compliance
- Backup procedures

## Emergency Contacts

### Technical Issues
- Hosting provider support
- Domain registrar support
- Email service provider
- CDN provider (if used)

### Content Issues
- Legal team for compliance
- Financial advisor for accuracy
- Marketing team for messaging
- Design team for visual updates

---

**Remember: This is a professional financial services website. Every element should reflect trustworthiness, expertise, and compliance with Canadian financial regulations.**