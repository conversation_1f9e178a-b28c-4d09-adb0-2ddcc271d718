/* Professional Financial Services Website Styles */

/* CSS Reset and Base Styles */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

/* CSS Custom Properties */
:root {
    /* Colors - Professional Financial Theme */
    --primary-blue: #1e3a8a;
    --primary-blue-light: #3b82f6;
    --secondary-green: #059669;
    --secondary-green-light: #10b981;
    --accent-gold: #d97706;
    --accent-gold-light: #f59e0b;
    
    /* Neutrals */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* Text Colors */
    --text-primary: var(--gray-900);
    --text-secondary: var(--gray-600);
    --text-muted: var(--gray-500);
    
    /* Background Colors */
    --bg-primary: var(--white);
    --bg-secondary: var(--gray-50);
    --bg-accent: var(--gray-100);
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-light) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--secondary-green) 0%, var(--secondary-green-light) 100%);
    --gradient-hero: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-green) 100%);
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Typography */
    --font-primary: 'Inter', system-ui, -apple-system, sans-serif;
    --font-heading: 'Merriweather', serif;
    
    /* Spacing - Compressed for better UX */
    --container-max-width: 1280px;
    --section-padding: 3rem 0;
    --section-padding-sm: 2rem 0;
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
}

/* Base Styles */
html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Screen Reader Only */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Container */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 1.5rem;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 700;
    line-height: 1.2;
    color: var(--text-primary);
}

h1 { font-size: 3.5rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 2rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1.125rem; }

p {
    margin-bottom: 1rem;
    line-height: 1.7;
}

/* Links */
a {
    color: var(--primary-blue);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover, a:focus {
    color: var(--primary-blue-light);
    text-decoration: underline;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    font-family: var(--font-primary);
    font-size: 0.95rem;
    font-weight: 600;
    text-decoration: none;
    border: 2px solid transparent;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--white);
    border-color: var(--primary-blue);
}

.btn-primary:hover, .btn-primary:focus {
    background: var(--primary-blue);
    color: var(--white);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-blue);
    border-color: var(--primary-blue);
}

.btn-secondary:hover, .btn-secondary:focus {
    background: var(--primary-blue);
    color: var(--white);
    text-decoration: none;
    transform: translateY(-2px);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--gray-200);
    z-index: 1000;
    transition: all var(--transition-fast);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 1.5rem;
    max-width: var(--container-max-width);
    margin: 0 auto;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    transition: var(--transition-fast);
}

.nav-brand:hover, .nav-brand:focus {
    text-decoration: none;
}

.nav-logo {
    height: 40px;
    width: auto;
}

.brand-text {
    font-family: var(--font-heading);
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-blue);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 1.5rem;
    align-items: center;
}

.nav-link {
    font-weight: 500;
    color: var(--text-primary);
    padding: 0.4rem 0;
    position: relative;
    transition: color var(--transition-fast);
    font-size: 0.95rem;
}

.nav-link:hover, .nav-link:focus {
    color: var(--primary-blue);
    text-decoration: none;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-blue);
    transition: width var(--transition-fast);
}

.nav-link:hover::after, .nav-link:focus::after {
    width: 100%;
}

.nav-link--cta {
    background: var(--gradient-primary);
    color: var(--white) !important;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    margin-left: 1rem;
}

.nav-link--cta::after {
    display: none;
}

.nav-link--cta:hover, .nav-link--cta:focus {
    background: var(--primary-blue);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.hamburger {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
}

.hamburger-line {
    width: 24px;
    height: 2px;
    background: var(--text-primary);
    transition: var(--transition-fast);
}

/* Hero Section */
.hero {
    padding: 5rem 0 3rem;
    background: var(--gradient-hero);
    color: var(--white);
    overflow: hidden;
    position: relative;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    pointer-events: none;
}

.hero-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 1.5rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.1;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2.5rem;
    opacity: 0.9;
    line-height: 1.6;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.hero-trust-indicators {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.trust-item {
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    backdrop-filter: blur(10px);
}

.hero-image {
    position: relative;
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
}

/* Statistics Section */
.statistics {
    padding: var(--section-padding);
    background: var(--bg-secondary);
}

.section-title {
    text-align: center;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.section-subtitle {
    text-align: center;
    color: var(--text-secondary);
    font-size: 1.125rem;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.stat-card {
    background: var(--bg-primary);
    padding: 2.5rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: transform var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 0.5rem;
    font-family: var(--font-heading);
}

.stat-label {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.stat-source {
    font-size: 0.875rem;
    color: var(--text-muted);
    font-style: italic;
}

/* Services Section */
.services {
    padding: var(--section-padding);
    background: var(--bg-primary);
}

.services-grid {
    display: grid;
    gap: 3rem;
}

.service-card {
    display: grid;
    grid-template-columns: 400px 1fr;
    gap: 2rem;
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.service-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.service-card:nth-child(even) {
    grid-template-columns: 1fr 400px;
}

.service-card:nth-child(even) .service-image {
    order: 2;
}

.service-image {
    overflow: hidden;
}

.service-image img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.service-card:hover .service-image img {
    transform: scale(1.05);
}

.service-content {
    padding: 2.5rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.service-content h3 {
    font-size: 1.75rem;
    margin-bottom: 1rem;
    color: var(--primary-blue);
}

.service-content p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.service-features {
    list-style: none;
    margin-bottom: 2rem;
}

.service-features li {
    position: relative;
    padding-left: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.service-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--secondary-green);
    font-weight: bold;
}

.service-link {
    font-weight: 600;
    color: var(--primary-blue);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: gap var(--transition-fast);
}

.service-link::after {
    content: '→';
    transition: transform var(--transition-fast);
}

.service-link:hover {
    gap: 0.75rem;
}

.service-link:hover::after {
    transform: translateX(4px);
}

/* Testimonials Section */
.testimonials {
    padding: var(--section-padding);
    background: var(--bg-secondary);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.testimonial-card {
    background: var(--bg-primary);
    padding: 2.5rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: transform var(--transition-normal);
}

.testimonial-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.testimonial-content blockquote {
    font-size: 1.125rem;
    line-height: 1.6;
    color: var(--text-secondary);
    font-style: italic;
    margin-bottom: 2rem;
    position: relative;
}

.testimonial-content blockquote::before {
    content: '"';
    font-size: 4rem;
    color: var(--primary-blue);
    position: absolute;
    top: -1rem;
    left: -1rem;
    font-family: serif;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.testimonial-photo {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--primary-blue);
}

.author-details h4 {
    font-size: 1.125rem;
    margin-bottom: 0.25rem;
    color: var(--text-primary);
}

.author-details p {
    color: var(--text-muted);
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.testimonial-rating {
    color: var(--accent-gold);
    font-size: 0.875rem;
}

/* Newsletter Section */
.newsletter {
    padding: var(--section-padding);
    background: var(--gradient-secondary);
    color: var(--white);
}

.newsletter-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.newsletter-content h2 {
    color: var(--white);
    margin-bottom: 1rem;
}

.newsletter-content p {
    font-size: 1.125rem;
    opacity: 0.9;
    margin-bottom: 2.5rem;
}

.newsletter-form {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.newsletter-form .form-group {
    flex: 1;
}

.newsletter-form input {
    width: 100%;
    padding: 1rem;
    border: none;
    border-radius: var(--radius-md);
    font-size: 1rem;
    background: var(--white);
    color: var(--text-primary);
}

.newsletter-form input:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

.form-help {
    display: block;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    opacity: 0.8;
}

.newsletter-features {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.feature-item {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    backdrop-filter: blur(10px);
}

/* Footer */
.footer {
    background: var(--gray-900);
    color: var(--gray-300);
    padding: 2.5rem 0 1.5rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1.5fr;
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-brand {
    max-width: 350px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
    text-decoration: none;
    transition: var(--transition-fast);
}

.footer-logo:hover {
    text-decoration: none;
    opacity: 0.8;
}

.footer-logo img {
    height: 32px;
    width: auto;
    filter: brightness(0) invert(1);
}

.footer-logo span {
    font-family: var(--font-heading);
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--white);
}

.footer-brand p {
    color: var(--gray-400);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    color: var(--gray-400);
    text-decoration: none;
    font-weight: 500;
    transition: color var(--transition-fast);
}

.social-link:hover {
    color: var(--white);
}

.footer-section h3 {
    color: var(--white);
    font-size: 1.125rem;
    margin-bottom: 1rem;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: var(--gray-400);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-links a:hover {
    color: var(--white);
}

.contact-info {
    font-style: normal;
    color: var(--gray-400);
    line-height: 1.6;
}

.contact-info a {
    color: var(--gray-300);
    text-decoration: none;
}

.contact-info a:hover {
    color: var(--white);
}

.footer-bottom {
    border-top: 1px solid var(--gray-700);
    padding-top: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-legal {
    display: flex;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.legal-links {
    display: flex;
    gap: 1.5rem;
}

.legal-links a {
    color: var(--gray-400);
    text-decoration: none;
    font-size: 0.875rem;
    transition: color var(--transition-fast);
}

.legal-links a:hover {
    color: var(--white);
}

.footer-certifications {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.cert-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    background: var(--primary-blue);
    color: var(--white);
    border-radius: var(--radius-sm);
    font-weight: 500;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 50px;
    height: 50px;
    background: var(--primary-blue);
    color: var(--white);
    border: none;
    border-radius: 50%;
    font-size: 1.25rem;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-fast);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: var(--primary-blue-light);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.875rem;
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-family: var(--font-primary);
    transition: border-color var(--transition-fast);
    background: var(--bg-primary);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

/* Responsive Design */
@media (max-width: 1024px) {
    :root {
        --section-padding: 2.5rem 0;
    }
    
    .hero-container {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }
    
    .service-card {
        grid-template-columns: 1fr !important;
    }
    
    .service-card:nth-child(even) .service-image {
        order: 0;
    }
    
    .service-image img {
        height: 250px;
    }
    
    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }
    
    .footer-brand {
        max-width: none;
    }
}

@media (max-width: 768px) {
    :root {
        --section-padding: 3rem 0;
    }
    
    .hamburger {
        display: flex;
    }
    
    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--white);
        flex-direction: column;
        padding: 2rem 0;
        box-shadow: var(--shadow-lg);
        transform: translateX(-100%);
        transition: transform var(--transition-normal);
    }
    
    .nav-menu.active {
        transform: translateX(0);
    }
    
    .nav-link--cta {
        margin-left: 0;
        margin-top: 1rem;
    }
    
    .hero {
        padding: 4rem 0 3rem;
    }
    
    .hero-title {
        font-size: 3rem;
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .testimonials-grid {
        grid-template-columns: 1fr;
    }
    
    .newsletter-form {
        flex-direction: column;
    }
    
    .newsletter-features {
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .footer-bottom {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }
    
    .social-links,
    .legal-links,
    .footer-certifications {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }
    
    .nav-container {
        padding: 1rem;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .stat-card,
    .testimonial-card,
    .service-content {
        padding: 2rem;
    }
    
    .back-to-top {
        bottom: 1rem;
        right: 1rem;
        width: 45px;
        height: 45px;
    }
}

/* Animation Classes */
.fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stat-number-animate {
    animation: countUp 2s ease-out;
}

@keyframes countUp {
    from {
        opacity: 0;
        transform: scale(0.5);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Focus Visible for Accessibility */
.btn:focus-visible,
.nav-link:focus-visible,
.social-link:focus-visible {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

/* Page Header Styles */
.page-header {
    background: var(--gradient-primary);
    color: var(--white);
    padding: 5rem 0 2.5rem;
    text-align: center;
}

.page-header h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--white);
}

.page-header p {
    font-size: 1.25rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* Content Section Styles */
.content-section {
    padding: var(--section-padding);
}

.content-section:nth-child(even) {
    background: var(--bg-secondary);
}

/* Mission Page Styles */
.mission {
    padding: var(--section-padding);
}

.mission-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.mission-text h2 {
    margin-bottom: 1.5rem;
}

.mission-points {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-top: 2rem;
}

.mission-point {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
}

.point-icon {
    flex-shrink: 0;
}

.point-icon img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.point-text h3 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
}

.mission-image img {
    width: 100%;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
}

/* Team Page Styles */
.team {
    padding: var(--section-padding);
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
}

.team-member {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.team-member:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.member-photo {
    height: 250px;
    overflow: hidden;
}

.member-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.member-info {
    padding: 2rem;
}

.member-info h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.member-title {
    color: var(--primary-blue);
    font-weight: 600;
    margin-bottom: 1rem;
}

.member-desc {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.member-credentials {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.member-credentials span {
    background: var(--primary-blue);
    color: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Why Choose Section */
.why-choose {
    padding: var(--section-padding);
    background: var(--bg-secondary);
}

/* Certifications */
.certifications {
    padding: var(--section-padding);
}

.cert-intro {
    text-align: center;
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.cert-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.cert-item {
    background: var(--bg-secondary);
    padding: 2rem;
    border-radius: var(--radius-lg);
    border-left: 4px solid var(--primary-blue);
}

.cert-item h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.cert-item p {
    color: var(--text-secondary);
    font-weight: 500;
}

/* Calculator Styles */
.calculator {
    background: var(--bg-primary);
    padding: 2.5rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    margin-bottom: 2rem;
}

.calculator h3 {
    font-size: 1.75rem;
    margin-bottom: 1rem;
    color: var(--primary-blue);
}

.calculator p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.calculator-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

/* Resource Lists */
.resource-list {
    list-style: none;
    padding: 0;
}

.resource-list li {
    margin-bottom: 0.75rem;
}

.resource-list a {
    color: var(--primary-blue);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-fast);
}

.resource-list a:hover {
    color: var(--primary-blue-light);
    text-decoration: underline;
}

/* Checklist Styles */
.checklist {
    list-style: none;
    padding: 0;
}

.checklist li {
    position: relative;
    padding-left: 2rem;
    margin-bottom: 0.75rem;
    color: var(--text-secondary);
}

.checklist li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--secondary-green);
    font-weight: bold;
    font-size: 1.2rem;
}

/* Hours List */
.hours-list {
    list-style: none;
    padding: 0;
}

.hours-list li {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Directions List */
.directions-list {
    list-style: none;
    padding: 0;
}

.directions-list li {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.directions-list strong {
    color: var(--text-primary);
}

/* Emergency Styles */
.emergency-contacts {
    padding: var(--section-padding);
    background: var(--bg-secondary);
}

.emergency-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.emergency-card {
    background: var(--bg-primary);
    padding: 2rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    border-left: 4px solid #dc2626;
}

.emergency-card h3 {
    color: #dc2626;
    font-size: 1.25rem;
    margin-bottom: 1rem;
}

.emergency-list {
    list-style: none;
    padding: 0;
}

.emergency-list li {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.emergency-list strong {
    color: var(--text-primary);
}

/* Warning Boxes */
.warning-box {
    background: #fef3c7;
    color: #92400e;
    padding: 1rem;
    border-radius: var(--radius-md);
    border-left: 4px solid #f59e0b;
    margin-top: 1.5rem;
    font-weight: 500;
}

/* Contact Form Styles */
.contact-form-container {
    max-width: 800px;
    margin: 0 auto;
}

.contact-form {
    background: var(--bg-primary);
    padding: 3rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.checkbox-group {
    margin-bottom: 1.5rem;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    cursor: pointer;
    font-size: 0.95rem;
    line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.checkmark {
    flex-shrink: 0;
}

/* Map Styles */
.map-container {
    height: 400px;
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    margin-top: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--gray-300);
}

.map-placeholder {
    text-align: center;
    color: var(--text-secondary);
    font-size: 1.1rem;
}

/* FAQ Styles */
.faq {
    padding: var(--section-padding);
    background: var(--bg-secondary);
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    margin-bottom: 1rem;
    overflow: hidden;
    transition: var(--transition-fast);
}

.faq-item:hover {
    box-shadow: var(--shadow-md);
}

.faq-question {
    padding: 1.5rem;
    background: var(--bg-secondary);
    cursor: pointer;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition-fast);
    position: relative;
}

.faq-question:hover {
    background: var(--primary-blue);
    color: var(--white);
}

.faq-question:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

.faq-question::after {
    content: '+';
    font-size: 1.5rem;
    transition: var(--transition-fast);
    flex-shrink: 0;
}

.faq-item.active .faq-question::after {
    transform: rotate(45deg);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.faq-answer div {
    padding: 1.5rem;
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Privacy Policy Styles */
.privacy-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 0;
}

.privacy-content h2 {
    font-size: 2rem;
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.privacy-content h3 {
    font-size: 1.5rem;
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.privacy-content p {
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 1rem;
}

.privacy-content ul,
.privacy-content ol {
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 1rem;
    padding-left: 2rem;
}

.policy-footer {
    margin-top: 2rem;
    padding: 2rem;
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border-left: 4px solid var(--primary-blue);
}

.policy-footer h3 {
    color: var(--primary-blue);
    margin-bottom: 1rem;
}

/* Error Page Styles */
.error-page {
    padding: 5rem 0 3rem;
    text-align: center;
    min-height: 70vh;
    display: flex;
    align-items: center;
}

.error-content {
    max-width: 800px;
    margin: 0 auto;
}

.error-number {
    font-size: 8rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 1rem;
    font-family: var(--font-heading);
}

.error-page h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.error-page p {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.error-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 2.5rem;
}

.helpful-links {
    margin-top: 2.5rem;
}

.helpful-links h3 {
    font-size: 1.5rem;
    margin-bottom: 2rem;
    color: var(--text-primary);
}

/* Print Styles */
@media print {
    .navbar,
    .back-to-top,
    .newsletter {
        display: none;
    }
    
    .hero {
        background: none;
        color: var(--text-primary);
    }
    
    .footer {
        background: none;
        color: var(--text-primary);
        border-top: 1px solid var(--gray-300);
    }
}

/* Responsive adjustments for additional pages */
@media (max-width: 1024px) {
    .mission-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }
    
    .calculator-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .page-header {
        padding: 4rem 0 2.5rem;
    }
    
    .page-header h1 {
        font-size: 2.5rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .contact-form {
        padding: 2rem;
    }
    
    .calculator {
        padding: 2rem;
    }
    
    .error-number {
        font-size: 6rem;
    }
    
    .error-page h1 {
        font-size: 2rem;
    }
    
    .error-actions {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 3.5rem 0 2rem;
    }
    
    .page-header h1 {
        font-size: 2rem;
    }
    
    .contact-form {
        padding: 1.5rem;
    }
    
    .calculator {
        padding: 1.5rem;
    }
    
    .error-number {
        font-size: 4rem;
    }
}