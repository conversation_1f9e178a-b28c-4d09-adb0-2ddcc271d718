// Professional Financial Services Website JavaScript
'use strict';

// DOM Content Loaded Event
document.addEventListener('DOMContentLoaded', function() {
    initializeNavigation();
    initializeAnimations();
    initializeCounters();
    initializeScrollEffects();
    initializeForms();
    initializeCalculators();
    initializeFAQ();
});

// Navigation functionality
function initializeNavigation() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    if (hamburger && navMenu) {
        hamburger.addEventListener('click', function() {
            const isExpanded = hamburger.getAttribute('aria-expanded') === 'true';
            
            hamburger.setAttribute('aria-expanded', !isExpanded);
            navMenu.classList.toggle('active');
            
            // Animate hamburger lines
            const lines = hamburger.querySelectorAll('.hamburger-line');
            if (navMenu.classList.contains('active')) {
                lines[0].style.transform = 'rotate(45deg) translate(6px, 6px)';
                lines[1].style.opacity = '0';
                lines[2].style.transform = 'rotate(-45deg) translate(6px, -6px)';
            } else {
                lines.forEach(line => {
                    line.style.transform = '';
                    line.style.opacity = '';
                });
            }
        });

        // Close mobile menu when clicking on a link
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                navMenu.classList.remove('active');
                hamburger.setAttribute('aria-expanded', 'false');
                
                const lines = hamburger.querySelectorAll('.hamburger-line');
                lines.forEach(line => {
                    line.style.transform = '';
                    line.style.opacity = '';
                });
            });
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!hamburger.contains(event.target) && !navMenu.contains(event.target)) {
                navMenu.classList.remove('active');
                hamburger.setAttribute('aria-expanded', 'false');
                
                const lines = hamburger.querySelectorAll('.hamburger-line');
                lines.forEach(line => {
                    line.style.transform = '';
                    line.style.opacity = '';
                });
            }
        });
    }

    // Update navbar background on scroll
    const navbar = document.querySelector('.navbar');
    if (navbar) {
        window.addEventListener('scroll', function() {
            if (window.scrollY > 100) {
                navbar.style.background = 'rgba(255, 255, 255, 0.98)';
                navbar.style.borderBottomColor = 'rgba(229, 231, 235, 0.8)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
                navbar.style.borderBottomColor = '#e5e7eb';
            }
        });
    }
}

// Animation functionality using Intersection Observer
function initializeAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in-up');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animatedElements = document.querySelectorAll(
        '.service-card, .testimonial-card, .stat-card, .hero-content'
    );
    
    animatedElements.forEach(element => {
        observer.observe(element);
    });
}

// Counter animation for statistics
function initializeCounters() {
    const counters = document.querySelectorAll('.stat-number[data-target]');
    
    const counterObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target;
                const target = parseInt(counter.getAttribute('data-target'));
                const duration = 2000; // 2 seconds
                const increment = target / (duration / 16); // 60 FPS
                let current = 0;
                
                counter.classList.add('stat-number-animate');
                
                const timer = setInterval(function() {
                    current += increment;
                    
                    if (current >= target) {
                        counter.textContent = formatNumber(target);
                        clearInterval(timer);
                    } else {
                        counter.textContent = formatNumber(Math.floor(current));
                    }
                }, 16);
                
                counterObserver.unobserve(counter);
            }
        });
    }, { threshold: 0.5 });
    
    counters.forEach(counter => {
        counterObserver.observe(counter);
    });
}

// Format numbers for display
function formatNumber(num) {
    if (num >= 1000000000) {
        return '$' + (num / 1000000000).toFixed(1) + 'B';
    } else if (num >= 1000000) {
        return '$' + (num / 1000000).toFixed(0) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(0) + 'K';
    }
    return num.toString();
}

// Scroll effects
function initializeScrollEffects() {
    const backToTopButton = document.getElementById('backToTop');
    
    if (backToTopButton) {
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTopButton.classList.add('visible');
            } else {
                backToTopButton.classList.remove('visible');
            }
        });
        
        backToTopButton.addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
}

// Form handling
function initializeForms() {
    // Newsletter form
    const newsletterForm = document.querySelector('.newsletter-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleNewsletterSubmission(this);
        });
    }
    
    // Contact form (if exists on page)
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleContactFormSubmission(this);
        });
    }
}

// Newsletter form submission
function handleNewsletterSubmission(form) {
    const emailInput = form.querySelector('input[type="email"]');
    const submitButton = form.querySelector('button[type="submit"]');
    const email = emailInput.value.trim();
    
    if (!isValidEmail(email)) {
        showFormMessage(form, 'Please enter a valid email address.', 'error');
        return;
    }
    
    // Disable form during submission
    submitButton.disabled = true;
    submitButton.textContent = 'Subscribing...';
    
    // Simulate API call (replace with actual implementation)
    setTimeout(function() {
        showFormMessage(form, 'Thank you for subscribing! Check your email for confirmation.', 'success');
        form.reset();
        submitButton.disabled = false;
        submitButton.textContent = 'Subscribe';
    }, 1000);
}

// Contact form submission
function handleContactFormSubmission(form) {
    const formData = new FormData(form);
    const submitButton = form.querySelector('button[type="submit"]');
    
    // Validate form
    if (!validateContactForm(form)) {
        return;
    }
    
    // Disable form during submission
    submitButton.disabled = true;
    submitButton.textContent = 'Sending...';
    
    // Simulate API call (replace with actual implementation)
    setTimeout(function() {
        showFormMessage(form, 'Thank you for your message. We\'ll get back to you within 24 hours.', 'success');
        form.reset();
        submitButton.disabled = false;
        submitButton.textContent = 'Send Message';
    }, 1500);
}

// Form validation
function validateContactForm(form) {
    const name = form.querySelector('#name')?.value.trim();
    const email = form.querySelector('#email')?.value.trim();
    const phone = form.querySelector('#phone')?.value.trim();
    const message = form.querySelector('#message')?.value.trim();
    
    clearFormErrors(form);
    let isValid = true;
    
    if (!name || name.length < 2) {
        showFieldError(form.querySelector('#name'), 'Name must be at least 2 characters long.');
        isValid = false;
    }
    
    if (!email || !isValidEmail(email)) {
        showFieldError(form.querySelector('#email'), 'Please enter a valid email address.');
        isValid = false;
    }
    
    if (phone && !isValidPhone(phone)) {
        showFieldError(form.querySelector('#phone'), 'Please enter a valid phone number.');
        isValid = false;
    }
    
    if (!message || message.length < 10) {
        showFieldError(form.querySelector('#message'), 'Message must be at least 10 characters long.');
        isValid = false;
    }
    
    return isValid;
}

// Email validation
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Phone validation (North American format)
function isValidPhone(phone) {
    const phoneRegex = /^(\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}$/;
    return phoneRegex.test(phone);
}

// Show form messages
function showFormMessage(form, message, type) {
    // Remove existing messages
    const existingMessage = form.querySelector('.form-message');
    if (existingMessage) {
        existingMessage.remove();
    }
    
    // Create new message
    const messageDiv = document.createElement('div');
    messageDiv.className = `form-message form-message--${type}`;
    messageDiv.textContent = message;
    messageDiv.style.cssText = `
        padding: 1rem;
        margin-top: 1rem;
        border-radius: 0.5rem;
        font-weight: 500;
        ${type === 'error' ? 
            'background: #fef2f2; color: #dc2626; border: 1px solid #fecaca;' : 
            'background: #f0fdf4; color: #166534; border: 1px solid #bbf7d0;'
        }
    `;
    
    form.appendChild(messageDiv);
    
    // Remove message after 5 seconds
    setTimeout(function() {
        if (messageDiv.parentNode) {
            messageDiv.remove();
        }
    }, 5000);
}

// Show field errors
function showFieldError(field, message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.textContent = message;
    errorDiv.style.cssText = `
        color: #dc2626;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    `;
    
    field.parentNode.appendChild(errorDiv);
    field.style.borderColor = '#dc2626';
}

// Clear form errors
function clearFormErrors(form) {
    const errors = form.querySelectorAll('.field-error');
    errors.forEach(error => error.remove());
    
    const fields = form.querySelectorAll('input, textarea, select');
    fields.forEach(field => {
        field.style.borderColor = '';
    });
}

// Calculator functionality
function initializeCalculators() {
    // Budget Calculator
    const budgetForm = document.getElementById('budget-calculator');
    if (budgetForm) {
        budgetForm.addEventListener('submit', function(e) {
            e.preventDefault();
            calculateBudget();
        });
    }
    
    // Mortgage Calculator
    const mortgageForm = document.getElementById('mortgage-calculator');
    if (mortgageForm) {
        mortgageForm.addEventListener('submit', function(e) {
            e.preventDefault();
            calculateMortgage();
        });
    }
    
    // Retirement Calculator
    const retirementForm = document.getElementById('retirement-calculator');
    if (retirementForm) {
        retirementForm.addEventListener('submit', function(e) {
            e.preventDefault();
            calculateRetirement();
        });
    }
}

// Budget Calculator
function calculateBudget() {
    const income = parseFloat(document.getElementById('monthly-income')?.value) || 0;
    const housing = parseFloat(document.getElementById('housing')?.value) || 0;
    const food = parseFloat(document.getElementById('food')?.value) || 0;
    const transportation = parseFloat(document.getElementById('transportation')?.value) || 0;
    const utilities = parseFloat(document.getElementById('utilities')?.value) || 0;
    const entertainment = parseFloat(document.getElementById('entertainment')?.value) || 0;
    const other = parseFloat(document.getElementById('other')?.value) || 0;
    
    const totalExpenses = housing + food + transportation + utilities + entertainment + other;
    const remaining = income - totalExpenses;
    const savingsRate = income > 0 ? (remaining / income * 100).toFixed(1) : 0;
    
    const resultDiv = document.getElementById('budget-result');
    if (resultDiv) {
        resultDiv.innerHTML = `
            <div class="calculator-result">
                <h3>Budget Analysis Results</h3>
                <div class="result-grid">
                    <div class="result-item">
                        <span class="result-label">Monthly Income:</span>
                        <span class="result-value">$${income.toLocaleString('en-CA', {minimumFractionDigits: 2})}</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">Total Expenses:</span>
                        <span class="result-value">$${totalExpenses.toLocaleString('en-CA', {minimumFractionDigits: 2})}</span>
                    </div>
                    <div class="result-item ${remaining >= 0 ? 'positive' : 'negative'}">
                        <span class="result-label">Remaining:</span>
                        <span class="result-value">$${remaining.toLocaleString('en-CA', {minimumFractionDigits: 2})}</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">Savings Rate:</span>
                        <span class="result-value">${savingsRate}%</span>
                    </div>
                </div>
                ${getbudgetRecommendation(remaining, income)}
            </div>
        `;
    }
}

// Mortgage Calculator
function calculateMortgage() {
    const loanAmount = parseFloat(document.getElementById('loan-amount')?.value) || 0;
    const interestRate = parseFloat(document.getElementById('interest-rate')?.value) || 0;
    const loanTerm = parseFloat(document.getElementById('loan-term')?.value) || 0;
    
    const monthlyRate = interestRate / 100 / 12;
    const numPayments = loanTerm * 12;
    
    let monthlyPayment = 0;
    if (monthlyRate > 0) {
        monthlyPayment = loanAmount * 
            (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) /
            (Math.pow(1 + monthlyRate, numPayments) - 1);
    } else {
        monthlyPayment = loanAmount / numPayments;
    }
    
    const totalPayment = monthlyPayment * numPayments;
    const totalInterest = totalPayment - loanAmount;
    
    const resultDiv = document.getElementById('mortgage-result');
    if (resultDiv) {
        resultDiv.innerHTML = `
            <div class="calculator-result">
                <h3>Mortgage Calculation Results</h3>
                <div class="result-grid">
                    <div class="result-item highlight">
                        <span class="result-label">Monthly Payment:</span>
                        <span class="result-value">$${monthlyPayment.toLocaleString('en-CA', {minimumFractionDigits: 2})}</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">Total Amount Paid:</span>
                        <span class="result-value">$${totalPayment.toLocaleString('en-CA', {minimumFractionDigits: 2})}</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">Total Interest:</span>
                        <span class="result-value">$${totalInterest.toLocaleString('en-CA', {minimumFractionDigits: 2})}</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">Interest Rate:</span>
                        <span class="result-value">${interestRate}%</span>
                    </div>
                </div>
                ${getMortgageRecommendation(monthlyPayment, loanAmount)}
            </div>
        `;
    }
}

// Retirement Calculator
function calculateRetirement() {
    const currentAge = parseFloat(document.getElementById('current-age')?.value) || 0;
    const retirementAge = parseFloat(document.getElementById('retirement-age')?.value) || 0;
    const currentSavings = parseFloat(document.getElementById('current-savings')?.value) || 0;
    const monthlyContribution = parseFloat(document.getElementById('monthly-contribution')?.value) || 0;
    const expectedReturn = parseFloat(document.getElementById('expected-return')?.value) || 0;
    
    const yearsToRetirement = retirementAge - currentAge;
    const monthsToRetirement = yearsToRetirement * 12;
    const monthlyReturn = expectedReturn / 100 / 12;
    
    let futureCurrentSavings = currentSavings;
    let futureContributions = 0;
    
    if (monthlyReturn > 0) {
        futureCurrentSavings = currentSavings * Math.pow(1 + monthlyReturn, monthsToRetirement);
        futureContributions = monthlyContribution * 
            ((Math.pow(1 + monthlyReturn, monthsToRetirement) - 1) / monthlyReturn);
    } else {
        futureContributions = monthlyContribution * monthsToRetirement;
    }
    
    const totalSavings = futureCurrentSavings + futureContributions;
    const monthlyRetirementIncome = totalSavings * 0.04 / 12; // 4% rule
    
    const resultDiv = document.getElementById('retirement-result');
    if (resultDiv) {
        resultDiv.innerHTML = `
            <div class="calculator-result">
                <h3>Retirement Savings Projection</h3>
                <div class="result-grid">
                    <div class="result-item">
                        <span class="result-label">Years to Retirement:</span>
                        <span class="result-value">${yearsToRetirement} years</span>
                    </div>
                    <div class="result-item highlight">
                        <span class="result-label">Projected Savings:</span>
                        <span class="result-value">$${totalSavings.toLocaleString('en-CA', {minimumFractionDigits: 0})}</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">Monthly Income (4% rule):</span>
                        <span class="result-value">$${monthlyRetirementIncome.toLocaleString('en-CA', {minimumFractionDigits: 2})}</span>
                    </div>
                    <div class="result-item">
                        <span class="result-label">Expected Annual Return:</span>
                        <span class="result-value">${expectedReturn}%</span>
                    </div>
                </div>
                ${getRetirementRecommendation(totalSavings, monthlyRetirementIncome)}
            </div>
        `;
    }
}

// Get budget recommendation
function getBudgetRecommendation(remaining, income) {
    if (remaining < 0) {
        return `<div class="recommendation warning">
            <h4>⚠️ Budget Alert</h4>
            <p>Your expenses exceed your income by $${Math.abs(remaining).toLocaleString('en-CA', {minimumFractionDigits: 2})}. Consider reducing expenses or increasing income.</p>
        </div>`;
    } else if (remaining / income < 0.1) {
        return `<div class="recommendation caution">
            <h4>💡 Savings Opportunity</h4>
            <p>You're saving less than 10% of your income. Try to increase your savings rate to at least 10-20% for better financial security.</p>
        </div>`;
    } else {
        return `<div class="recommendation success">
            <h4>✅ Great Job!</h4>
            <p>You have a healthy budget with ${((remaining/income)*100).toFixed(1)}% savings rate. Consider maximizing your RRSP and TFSA contributions.</p>
        </div>`;
    }
}

// Get mortgage recommendation
function getMortgageRecommendation(monthlyPayment, loanAmount) {
    return `<div class="recommendation info">
        <h4>💡 Mortgage Tips</h4>
        <ul>
            <li>Ensure your housing costs don't exceed 32% of your gross income</li>
            <li>Consider bi-weekly payments to reduce interest</li>
            <li>Shop around for the best mortgage rates</li>
            <li>Factor in property taxes, insurance, and maintenance costs</li>
        </ul>
    </div>`;
}

// Get retirement recommendation
function getRetirementRecommendation(totalSavings, monthlyIncome) {
    return `<div class="recommendation info">
        <h4>🎯 Retirement Planning Tips</h4>
        <ul>
            <li>Maximize your RRSP contributions for tax benefits</li>
            <li>Use TFSA for tax-free growth</li>
            <li>Consider CPP and OAS in your retirement planning</li>
            <li>Review and adjust your plan every 5 years</li>
            <li>Consult with a financial advisor for personalized advice</li>
        </ul>
    </div>`;
}

// FAQ functionality
function initializeFAQ() {
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        const answer = item.querySelector('.faq-answer');
        
        if (question && answer) {
            question.addEventListener('click', function() {
                const isActive = item.classList.contains('active');
                
                // Close all FAQ items
                faqItems.forEach(faqItem => {
                    faqItem.classList.remove('active');
                    const faqAnswer = faqItem.querySelector('.faq-answer');
                    if (faqAnswer) {
                        faqAnswer.style.maxHeight = null;
                    }
                });
                
                // Open clicked item if it wasn't active
                if (!isActive) {
                    item.classList.add('active');
                    answer.style.maxHeight = answer.scrollHeight + 'px';
                }
            });
            
            // Add keyboard support
            question.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    question.click();
                }
            });
        }
    });
}

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Performance optimization: Lazy load images
function initializeLazyLoading() {
    const images = document.querySelectorAll('img[loading="lazy"]');
    
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver(function(entries, observer) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src || img.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        images.forEach(img => imageObserver.observe(img));
    } else {
        // Fallback for browsers without IntersectionObserver
        images.forEach(img => {
            img.src = img.dataset.src || img.src;
        });
    }
}

// Error handling
window.addEventListener('error', function(e) {
    console.error('JavaScript Error:', e.error);
    // In production, you might want to send this to a logging service
});

// Unhandled promise rejection handler
window.addEventListener('unhandledrejection', function(e) {
    console.error('Unhandled Promise Rejection:', e.reason);
    // In production, you might want to send this to a logging service
});

// Export functions for testing (if needed)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        isValidEmail,
        isValidPhone,
        formatNumber,
        calculateBudget,
        calculateMortgage,
        calculateRetirement
    };
}