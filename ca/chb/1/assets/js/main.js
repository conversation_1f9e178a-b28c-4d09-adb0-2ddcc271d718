// Mobile Navigation
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');

hamburger.addEventListener('click', () => {
    hamburger.classList.toggle('active');
    navMenu.classList.toggle('active');
});

// Close mobile menu when clicking on a link
document.querySelectorAll('.nav-link').forEach(n => n.addEventListener('click', () => {
    hamburger.classList.remove('active');
    navMenu.classList.remove('active');
}));

// Back to Top Button
const backToTopButton = document.getElementById('backToTop');

window.addEventListener('scroll', () => {
    if (window.pageYOffset > 300) {
        backToTopButton.classList.add('visible');
    } else {
        backToTopButton.classList.remove('visible');
    }
});

backToTopButton.addEventListener('click', () => {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
});

// Newsletter Form
const newsletterForm = document.querySelector('.newsletter-form');
if (newsletterForm) {
    newsletterForm.addEventListener('submit', (e) => {
        e.preventDefault();
        const email = e.target.querySelector('input[type="email"]').value;
        
        if (email) {
            alert('Дякуємо за підписку! Ви отримаєте підтвердження на email: ' + email);
            e.target.reset();
        }
    });
}

// Contact Form Validation
const contactForm = document.querySelector('#contact-form');
if (contactForm) {
    contactForm.addEventListener('submit', (e) => {
        e.preventDefault();
        
        const name = document.getElementById('name').value.trim();
        const email = document.getElementById('email').value.trim();
        const phone = document.getElementById('phone').value.trim();
        const message = document.getElementById('message').value.trim();
        
        let isValid = true;
        
        // Clear previous error messages
        document.querySelectorAll('.error-message').forEach(error => error.remove());
        
        // Validate name
        if (name.length < 2) {
            showError('name', "Ім'я повинно містити принаймні 2 символи");
            isValid = false;
        }
        
        // Validate email
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            showError('email', 'Введіть дійсну email адресу');
            isValid = false;
        }
        
        // Validate phone (Canadian format)
        const phoneRegex = /^(\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}$/;
        if (phone && !phoneRegex.test(phone)) {
            showError('phone', 'Введіть дійсний номер телефону');
            isValid = false;
        }
        
        // Validate message
        if (message.length < 10) {
            showError('message', 'Повідомлення повинно містити принаймні 10 символів');
            isValid = false;
        }
        
        if (isValid) {
            alert('Дякуємо за повідомлення! Ми зв\'яжемося з вами найближчим часом.');
            contactForm.reset();
        }
    });
}

function showError(fieldId, message) {
    const field = document.getElementById(fieldId);
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.style.color = '#ef4444';
    errorDiv.style.fontSize = '0.875rem';
    errorDiv.style.marginTop = '0.25rem';
    errorDiv.textContent = message;
    field.parentNode.insertBefore(errorDiv, field.nextSibling);
}

// FAQ Accordion
function initFAQ() {
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        const answer = item.querySelector('.faq-answer');
        
        question.addEventListener('click', () => {
            const isActive = item.classList.contains('active');
            
            // Close all FAQ items
            faqItems.forEach(faqItem => {
                faqItem.classList.remove('active');
                faqItem.querySelector('.faq-answer').style.maxHeight = null;
            });
            
            // Open clicked item if it wasn't active
            if (!isActive) {
                item.classList.add('active');
                answer.style.maxHeight = answer.scrollHeight + 'px';
            }
        });
    });
}

// Financial Calculators
function initCalculators() {
    // Budget Calculator
    const budgetForm = document.querySelector('#budget-calculator');
    if (budgetForm) {
        budgetForm.addEventListener('submit', (e) => {
            e.preventDefault();
            calculateBudget();
        });
    }
    
    // Mortgage Calculator
    const mortgageForm = document.querySelector('#mortgage-calculator');
    if (mortgageForm) {
        mortgageForm.addEventListener('submit', (e) => {
            e.preventDefault();
            calculateMortgage();
        });
    }
    
    // Retirement Calculator
    const retirementForm = document.querySelector('#retirement-calculator');
    if (retirementForm) {
        retirementForm.addEventListener('submit', (e) => {
            e.preventDefault();
            calculateRetirement();
        });
    }
}

function calculateBudget() {
    const income = parseFloat(document.getElementById('monthly-income').value) || 0;
    const housing = parseFloat(document.getElementById('housing').value) || 0;
    const food = parseFloat(document.getElementById('food').value) || 0;
    const transportation = parseFloat(document.getElementById('transportation').value) || 0;
    const utilities = parseFloat(document.getElementById('utilities').value) || 0;
    const entertainment = parseFloat(document.getElementById('entertainment').value) || 0;
    const other = parseFloat(document.getElementById('other').value) || 0;
    
    const totalExpenses = housing + food + transportation + utilities + entertainment + other;
    const remaining = income - totalExpenses;
    
    const resultDiv = document.getElementById('budget-result');
    resultDiv.innerHTML = `
        <div class="calculator-result">
            <h3>Результат розрахунку бюджету</h3>
            <div class="result-item">
                <span>Загальний дохід:</span>
                <span>$${income.toFixed(2)}</span>
            </div>
            <div class="result-item">
                <span>Загальні витрати:</span>
                <span>$${totalExpenses.toFixed(2)}</span>
            </div>
            <div class="result-item ${remaining >= 0 ? 'positive' : 'negative'}">
                <span>Залишається:</span>
                <span>$${remaining.toFixed(2)}</span>
            </div>
            ${remaining < 0 ? '<p class="warning">Увага! Ваші витрати перевищують дохід.</p>' : ''}
            ${remaining > 0 ? '<p class="success">Чудово! У вас є можливість заощаджувати.</p>' : ''}
        </div>
    `;
}

function calculateMortgage() {
    const loanAmount = parseFloat(document.getElementById('loan-amount').value) || 0;
    const interestRate = parseFloat(document.getElementById('interest-rate').value) || 0;
    const loanTerm = parseFloat(document.getElementById('loan-term').value) || 0;
    
    const monthlyRate = interestRate / 100 / 12;
    const numPayments = loanTerm * 12;
    
    const monthlyPayment = loanAmount * 
        (monthlyRate * Math.pow(1 + monthlyRate, numPayments)) /
        (Math.pow(1 + monthlyRate, numPayments) - 1);
    
    const totalPayment = monthlyPayment * numPayments;
    const totalInterest = totalPayment - loanAmount;
    
    const resultDiv = document.getElementById('mortgage-result');
    resultDiv.innerHTML = `
        <div class="calculator-result">
            <h3>Результат розрахунку іпотеки</h3>
            <div class="result-item">
                <span>Щомісячний платіж:</span>
                <span>$${monthlyPayment.toFixed(2)}</span>
            </div>
            <div class="result-item">
                <span>Загальна сума виплат:</span>
                <span>$${totalPayment.toFixed(2)}</span>
            </div>
            <div class="result-item">
                <span>Загальні відсотки:</span>
                <span>$${totalInterest.toFixed(2)}</span>
            </div>
        </div>
    `;
}

function calculateRetirement() {
    const currentAge = parseFloat(document.getElementById('current-age').value) || 0;
    const retirementAge = parseFloat(document.getElementById('retirement-age').value) || 0;
    const currentSavings = parseFloat(document.getElementById('current-savings').value) || 0;
    const monthlyContribution = parseFloat(document.getElementById('monthly-contribution').value) || 0;
    const expectedReturn = parseFloat(document.getElementById('expected-return').value) || 0;
    
    const yearsToRetirement = retirementAge - currentAge;
    const monthsToRetirement = yearsToRetirement * 12;
    const monthlyReturn = expectedReturn / 100 / 12;
    
    // Future value of current savings
    const futureCurrentSavings = currentSavings * Math.pow(1 + monthlyReturn, monthsToRetirement);
    
    // Future value of monthly contributions
    const futureContributions = monthlyContribution * 
        ((Math.pow(1 + monthlyReturn, monthsToRetirement) - 1) / monthlyReturn);
    
    const totalSavings = futureCurrentSavings + futureContributions;
    
    const resultDiv = document.getElementById('retirement-result');
    resultDiv.innerHTML = `
        <div class="calculator-result">
            <h3>Прогноз пенсійних заощаджень</h3>
            <div class="result-item">
                <span>Років до пенсії:</span>
                <span>${yearsToRetirement}</span>
            </div>
            <div class="result-item">
                <span>Прогнозована сума заощаджень:</span>
                <span>$${totalSavings.toFixed(2)}</span>
            </div>
            <div class="result-item">
                <span>Щомісячний дохід з 4% правила:</span>
                <span>$${(totalSavings * 0.04 / 12).toFixed(2)}</span>
            </div>
        </div>
    `;
}

// Intersection Observer for animations
function initAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in-up');
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    document.querySelectorAll('.feature-card, .testimonial-card, .stat-item').forEach(el => {
        observer.observe(el);
    });
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    initFAQ();
    initCalculators();
    initAnimations();
});

// Navbar scroll effect
window.addEventListener('scroll', () => {
    const navbar = document.querySelector('.navbar');
    if (window.scrollY > 100) {
        navbar.style.background = 'rgba(255, 255, 255, 0.98)';
    } else {
        navbar.style.background = 'rgba(255, 255, 255, 0.95)';
    }
});