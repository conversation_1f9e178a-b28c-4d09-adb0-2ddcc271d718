/* Page-specific styles */

/* <PERSON> Header */
.page-header {
    background: var(--gradient-primary);
    color: white;
    padding: 120px 0 80px;
    text-align: center;
}

.page-header h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.page-header p {
    font-size: 1.25rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* Active nav link */
.nav-link.active {
    color: var(--primary-color);
}

.nav-link.active::after {
    width: 100%;
}

/* Mission Section */
.mission {
    padding: 80px 0;
}

.mission-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.mission-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--text-dark);
}

.mission-text p {
    font-size: 1.1rem;
    color: var(--text-light);
    margin-bottom: 2rem;
    line-height: 1.7;
}

.mission-points {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.mission-point {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.point-icon {
    font-size: 2rem;
    background: var(--background-light);
    padding: 1rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 60px;
    height: 60px;
}

.point-text h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.point-text p {
    color: var(--text-light);
    margin: 0;
}

.mission-image img {
    width: 100%;
    border-radius: 16px;
    box-shadow: var(--shadow-lg);
}

/* Values Section */
.values {
    padding: 80px 0;
    background: var(--background-light);
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.value-card {
    background: white;
    padding: 2.5rem;
    border-radius: 12px;
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
}

.value-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.value-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
}

.value-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.value-card p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Team Section */
.team {
    padding: 80px 0;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
}

.team-member {
    background: white;
    border-radius: 16px;
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
}

.team-member:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.member-photo {
    height: 250px;
    overflow: hidden;
}

.member-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.team-member:hover .member-photo img {
    transform: scale(1.05);
}

.member-info {
    padding: 2rem;
}

.member-info h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.member-title {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.member-desc {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.member-credentials {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.member-credentials span {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

/* Why Choose Section */
.why-choose {
    padding: 80px 0;
    background: var(--background-light);
}

.choose-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.choose-item {
    text-align: center;
    background: white;
    padding: 2.5rem;
    border-radius: 12px;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.choose-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.choose-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.choose-item h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.choose-item p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Certifications Section */
.certifications {
    padding: 80px 0;
}

.cert-intro {
    text-align: center;
    font-size: 1.1rem;
    color: var(--text-light);
    margin-bottom: 3rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.cert-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.cert-item {
    background: var(--background-light);
    padding: 2rem;
    border-radius: 12px;
    border-left: 4px solid var(--primary-color);
}

.cert-item h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.cert-item p {
    color: var(--text-light);
    font-weight: 500;
}

/* CTA Section */
.cta {
    padding: 80px 0;
    background: var(--gradient-secondary);
    color: white;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Content Pages Styles */
.content-section {
    padding: 60px 0;
}

.content-section:nth-child(even) {
    background: var(--background-light);
}

.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.content-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.content-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.content-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.content-card p {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.content-list {
    list-style: none;
    padding: 0;
}

.content-list li {
    padding: 0.5rem 0;
    padding-left: 2rem;
    position: relative;
    color: var(--text-light);
}

.content-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--secondary-color);
    font-weight: bold;
}

/* FAQ Styles */
.faq {
    padding: 80px 0;
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: white;
    border-radius: 12px;
    box-shadow: var(--shadow);
    margin-bottom: 1rem;
    overflow: hidden;
    transition: var(--transition);
}

.faq-question {
    padding: 1.5rem;
    background: var(--background-light);
    cursor: pointer;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition);
}

.faq-question:hover {
    background: var(--primary-color);
    color: white;
}

.faq-question::after {
    content: '+';
    font-size: 1.5rem;
    transition: var(--transition);
}

.faq-item.active .faq-question::after {
    transform: rotate(45deg);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.faq-answer div {
    padding: 1.5rem;
    color: var(--text-light);
    line-height: 1.6;
}

/* Calculator Styles */
.calculator {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
}

.calculator h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--text-dark);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-dark);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.calculator-result {
    margin-top: 2rem;
    padding: 1.5rem;
    background: var(--background-light);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.result-item:last-child {
    border-bottom: none;
}

.result-item.positive {
    color: var(--secondary-color);
    font-weight: 600;
}

.result-item.negative {
    color: #ef4444;
    font-weight: 600;
}

.warning {
    background: #fef3c7;
    color: #92400e;
    padding: 1rem;
    border-radius: 8px;
    margin-top: 1rem;
    border-left: 4px solid #f59e0b;
}

.success {
    background: #d1fae5;
    color: #065f46;
    padding: 1rem;
    border-radius: 8px;
    margin-top: 1rem;
    border-left: 4px solid var(--secondary-color);
}

/* Contact Form Styles */
.contact-form {
    background: white;
    padding: 3rem;
    border-radius: 16px;
    box-shadow: var(--shadow-lg);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.error-message {
    color: #ef4444;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Map Container */
.map-container {
    height: 400px;
    background: var(--background-light);
    border-radius: 12px;
    overflow: hidden;
    margin-top: 2rem;
    position: relative;
}

.map-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    font-size: 1.1rem;
}

/* Privacy Policy Styles */
.privacy-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 0;
}

.privacy-content h2 {
    font-size: 2rem;
    font-weight: 600;
    margin-top: 3rem;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.privacy-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.privacy-content p {
    color: var(--text-light);
    line-height: 1.7;
    margin-bottom: 1rem;
}

.privacy-content ul,
.privacy-content ol {
    color: var(--text-light);
    line-height: 1.7;
    margin-bottom: 1rem;
    padding-left: 2rem;
}

/* Prevention Tips Styles */
.prevention-tips {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.tip-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
}

.tip-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.tip-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
}

.tip-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.tip-card p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Responsive Design for Pages */
@media (max-width: 768px) {
    .page-header h1 {
        font-size: 2rem;
    }
    
    .page-header p {
        font-size: 1rem;
    }
    
    .mission-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .mission-text h2 {
        font-size: 2rem;
    }
    
    .team-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .cta-content h2 {
        font-size: 2rem;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .contact-form {
        padding: 2rem;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 100px 0 60px;
    }
    
    .content-section {
        padding: 40px 0;
    }
    
    .value-card,
    .content-card,
    .calculator {
        padding: 1.5rem;
    }
    
    .member-info {
        padding: 1.5rem;
    }
    
    .contact-form {
        padding: 1.5rem;
    }
}