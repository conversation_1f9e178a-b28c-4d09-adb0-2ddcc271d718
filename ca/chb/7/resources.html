<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Financial Resources & Calculators - Canadian Financial Security Group</title>
    <meta name="description" content="Free financial calculators, educational resources, and tools to help Canadians make informed financial decisions.">
    <link rel="stylesheet" href="assets/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Montserrat:wght@400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <header class="header">
        <nav class="nav">
            <div class="container">
                <div class="nav-brand">
                    <a href="index.html" class="logo-link">
                        <div class="logo">CFSG</div>
                        <span class="brand-text">Canadian Financial Security Group</span>
                    </a>
                </div>
                <ul class="nav-menu">
                    <li><a href="index.html" class="nav-link">Home</a></li>
                    <li><a href="about.html" class="nav-link">About Us</a></li>
                    <li><a href="financial-literacy.html" class="nav-link">Financial Literacy</a></li>
                    <li><a href="fraud-protection.html" class="nav-link">Fraud Protection</a></li>
                    <li><a href="resources.html" class="nav-link active">Resources</a></li>
                    <li><a href="contact.html" class="nav-link">Contact</a></li>
                </ul>
                <div class="nav-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </nav>
    </header>

    <section class="page-hero">
        <div class="hero-background">
            <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80" alt="Financial Tools and Resources" class="hero-image">
        </div>
        <div class="container">
            <div class="page-hero-content">
                <h1 class="page-title">Financial Resources & Tools</h1>
                <p class="page-subtitle">Free calculators, guides, and educational resources to support your financial journey</p>
            </div>
        </div>
    </section>

    <section class="calculators-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Interactive Financial Calculators</h2>
                <p class="section-subtitle">Plan your financial future with our professional-grade calculators</p>
            </div>
            
            <!-- Mortgage Calculator -->
            <div class="calculator-card">
                <div class="calculator-header">
                    <h3 class="calculator-title">🏠 Mortgage Calculator</h3>
                    <p class="calculator-description">Calculate your monthly mortgage payments and total interest costs</p>
                </div>
                <div class="calculator-content">
                    <div class="calculator-inputs">
                        <div class="input-group">
                            <label for="principal">Home Price (CAD)</label>
                            <input type="number" id="principal" placeholder="500000" min="0" step="1000">
                        </div>
                        <div class="input-group">
                            <label for="down-payment">Down Payment (%)</label>
                            <input type="number" id="down-payment" placeholder="20" min="0" max="100" step="1">
                        </div>
                        <div class="input-group">
                            <label for="rate">Interest Rate (%)</label>
                            <input type="number" id="rate" placeholder="5.5" min="0" max="20" step="0.1">
                        </div>
                        <div class="input-group">
                            <label for="years">Amortization (Years)</label>
                            <input type="number" id="years" placeholder="25" min="1" max="35" step="1">
                        </div>
                        <button onclick="calculateMortgage()" class="btn btn-primary">Calculate</button>
                    </div>
                    <div class="calculator-results">
                        <div class="result-item">
                            <span class="result-label">Monthly Payment:</span>
                            <span class="result-value" id="monthly-payment">$0</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">Total Payment:</span>
                            <span class="result-value" id="total-payment">$0</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">Total Interest:</span>
                            <span class="result-value" id="total-interest">$0</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">Loan Amount:</span>
                            <span class="result-value" id="loan-amount">$0</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- RRSP Calculator -->
            <div class="calculator-card">
                <div class="calculator-header">
                    <h3 class="calculator-title">💰 RRSP Calculator</h3>
                    <p class="calculator-description">Plan your retirement savings with RRSP contributions</p>
                </div>
                <div class="calculator-content">
                    <div class="calculator-inputs">
                        <div class="input-group">
                            <label for="monthly-contribution">Monthly Contribution (CAD)</label>
                            <input type="number" id="monthly-contribution" placeholder="500" min="0" step="50">
                        </div>
                        <div class="input-group">
                            <label for="annual-return">Expected Annual Return (%)</label>
                            <input type="number" id="annual-return" placeholder="7" min="0" max="20" step="0.1">
                        </div>
                        <div class="input-group">
                            <label for="investment-years">Investment Period (Years)</label>
                            <input type="number" id="investment-years" placeholder="30" min="1" max="50" step="1">
                        </div>
                        <div class="input-group">
                            <label for="current-age">Current Age</label>
                            <input type="number" id="current-age" placeholder="35" min="18" max="70" step="1">
                        </div>
                        <button onclick="calculateRRSP()" class="btn btn-primary">Calculate</button>
                    </div>
                    <div class="calculator-results">
                        <div class="result-item">
                            <span class="result-label">Future Value:</span>
                            <span class="result-value" id="future-value">$0</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">Total Contributions:</span>
                            <span class="result-value" id="total-contributions">$0</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">Investment Growth:</span>
                            <span class="result-value" id="total-growth">$0</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">Retirement Age:</span>
                            <span class="result-value" id="retirement-age">0</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Budget Calculator -->
            <div class="calculator-card">
                <div class="calculator-header">
                    <h3 class="calculator-title">📊 Budget Calculator</h3>
                    <p class="calculator-description">Analyze your monthly budget and get personalized recommendations</p>
                </div>
                <div class="calculator-content">
                    <div class="calculator-inputs">
                        <div class="input-group">
                            <label for="monthly-income">Monthly Income (CAD)</label>
                            <input type="number" id="monthly-income" placeholder="5000" min="0" step="100">
                        </div>
                        <div class="input-group">
                            <label for="housing">Housing Costs</label>
                            <input type="number" id="housing" placeholder="1500" min="0" step="50">
                        </div>
                        <div class="input-group">
                            <label for="transportation">Transportation</label>
                            <input type="number" id="transportation" placeholder="400" min="0" step="25">
                        </div>
                        <div class="input-group">
                            <label for="food">Food & Groceries</label>
                            <input type="number" id="food" placeholder="600" min="0" step="25">
                        </div>
                        <div class="input-group">
                            <label for="utilities">Utilities</label>
                            <input type="number" id="utilities" placeholder="200" min="0" step="25">
                        </div>
                        <div class="input-group">
                            <label for="entertainment">Entertainment</label>
                            <input type="number" id="entertainment" placeholder="300" min="0" step="25">
                        </div>
                        <div class="input-group">
                            <label for="other">Other Expenses</label>
                            <input type="number" id="other" placeholder="200" min="0" step="25">
                        </div>
                        <button onclick="calculateBudget()" class="btn btn-primary">Calculate</button>
                    </div>
                    <div class="calculator-results">
                        <div class="result-item">
                            <span class="result-label">Total Expenses:</span>
                            <span class="result-value" id="total-expenses">$0</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">Remaining Income:</span>
                            <span class="result-value" id="remaining-income">$0</span>
                        </div>
                        <div class="result-item">
                            <span class="result-label">Savings Rate:</span>
                            <span class="result-value" id="savings-rate">0%</span>
                        </div>
                        <div class="budget-recommendations">
                            <h4>Recommendations:</h4>
                            <ul id="budget-recommendations"></ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="educational-resources">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Educational Resources</h2>
                <p class="section-subtitle">Comprehensive guides and tools for financial success</p>
            </div>
            <div class="resources-grid">
                <div class="resource-card">
                    <div class="resource-icon">📚</div>
                    <h3 class="resource-title">Financial Planning Templates</h3>
                    <p class="resource-description">Downloadable Excel templates for budgeting, investment tracking, and retirement planning.</p>
                    <a href="#" class="resource-link">Download Templates</a>
                </div>
                
                <div class="resource-card">
                    <div class="resource-icon">📖</div>
                    <h3 class="resource-title">Canadian Tax Guide</h3>
                    <p class="resource-description">Comprehensive guide to Canadian tax planning, deductions, and optimization strategies.</p>
                    <a href="#" class="resource-link">Read Guide</a>
                </div>
                
                <div class="resource-card">
                    <div class="resource-icon">🎯</div>
                    <h3 class="resource-title">Investment Basics Course</h3>
                    <p class="resource-description">Free online course covering investment fundamentals for Canadian investors.</p>
                    <a href="#" class="resource-link">Start Course</a>
                </div>
                
                <div class="resource-card">
                    <div class="resource-icon">🛡️</div>
                    <h3 class="resource-title">Fraud Prevention Checklist</h3>
                    <p class="resource-description">Essential checklist to protect yourself from financial fraud and identity theft.</p>
                    <a href="#" class="resource-link">Download Checklist</a>
                </div>
                
                <div class="resource-card">
                    <div class="resource-icon">📊</div>
                    <h3 class="resource-title">Market Insights Report</h3>
                    <p class="resource-description">Monthly analysis of Canadian financial markets and economic trends.</p>
                    <a href="#" class="resource-link">View Report</a>
                </div>
                
                <div class="resource-card">
                    <div class="resource-icon">💡</div>
                    <h3 class="resource-title">Financial Tips Blog</h3>
                    <p class="resource-description">Regular articles on personal finance, investment strategies, and money management.</p>
                    <a href="#" class="resource-link">Read Blog</a>
                </div>
            </div>
        </div>
    </section>

    <section class="canadian-resources">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Official Canadian Financial Resources</h2>
                <p class="section-subtitle">Links to government agencies and regulatory bodies</p>
            </div>
            <div class="official-links-grid">
                <div class="official-link-card">
                    <h3 class="official-title">🏛️ Government of Canada</h3>
                    <ul class="official-links">
                        <li><a href="https://www.canada.ca/en/financial-consumer-agency.html" target="_blank">Financial Consumer Agency of Canada</a></li>
                        <li><a href="https://www.canada.ca/en/revenue-agency.html" target="_blank">Canada Revenue Agency</a></li>
                        <li><a href="https://www.osfi-bsif.gc.ca/" target="_blank">Office of the Superintendent of Financial Institutions</a></li>
                        <li><a href="https://www.cdic.ca/" target="_blank">Canada Deposit Insurance Corporation</a></li>
                    </ul>
                </div>
                
                <div class="official-link-card">
                    <h3 class="official-title">📈 Investment Regulators</h3>
                    <ul class="official-links">
                        <li><a href="https://www.iiroc.ca/" target="_blank">Investment Industry Regulatory Organization of Canada</a></li>
                        <li><a href="https://www.mfda.ca/" target="_blank">Mutual Fund Dealers Association of Canada</a></li>
                        <li><a href="https://www.osc.gov.on.ca/" target="_blank">Ontario Securities Commission</a></li>
                        <li><a href="https://www.bcsc.bc.ca/" target="_blank">British Columbia Securities Commission</a></li>
                    </ul>
                </div>
                
                <div class="official-link-card">
                    <h3 class="official-title">🛡️ Consumer Protection</h3>
                    <ul class="official-links">
                        <li><a href="https://www.antifraudcentre-centreantifraude.ca/" target="_blank">Canadian Anti-Fraud Centre</a></li>
                        <li><a href="https://www.competitionbureau.gc.ca/" target="_blank">Competition Bureau Canada</a></li>
                        <li><a href="https://www.priv.gc.ca/" target="_blank">Office of the Privacy Commissioner</a></li>
                        <li><a href="https://www.fcac-acfc.gc.ca/" target="_blank">Financial Consumer Agency of Canada</a></li>
                    </ul>
                </div>
                
                <div class="official-link-card">
                    <h3 class="official-title">💼 Professional Bodies</h3>
                    <ul class="official-links">
                        <li><a href="https://www.cpacanada.ca/" target="_blank">CPA Canada</a></li>
                        <li><a href="https://www.fpcanada.ca/" target="_blank">FP Canada (CFP Board)</a></li>
                        <li><a href="https://www.cfa.org/" target="_blank">CFA Institute</a></li>
                        <li><a href="https://www.advocis.ca/" target="_blank">Advocis - The Financial Advisors Association</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <section class="newsletter-signup">
        <div class="container">
            <div class="newsletter-content">
                <h2 class="newsletter-title">Stay Informed with Our Financial Newsletter</h2>
                <p class="newsletter-description">Get monthly insights, tips, and updates on Canadian financial markets and regulations.</p>
                <form class="newsletter-form">
                    <div class="form-group">
                        <input type="email" id="newsletter-email" placeholder="Enter your email address" required>
                        <button type="submit" class="btn btn-primary">Subscribe</button>
                    </div>
                    <p class="newsletter-privacy">We respect your privacy. Unsubscribe at any time.</p>
                </form>
            </div>
        </div>
    </section>

    <section class="cta">
        <div class="container">
            <div class="cta-content">
                <h2 class="cta-title">Need Personalized Financial Guidance?</h2>
                <p class="cta-subtitle">Our expert advisors are ready to help you achieve your financial goals</p>
                <div class="cta-buttons">
                    <a href="contact.html" class="btn btn-primary">Schedule Consultation</a>
                    <a href="financial-literacy.html" class="btn btn-secondary">Explore Our Services</a>
                </div>
            </div>
        </div>
    </section>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3 class="footer-title">Canadian Financial Security Group</h3>
                    <p class="footer-description">Professional financial literacy education and fraud protection services for Canadians.</p>
                    <div class="contact-info">
                        <p>📞 +1 (604) 555-2847</p>
                        <p>📧 <EMAIL></p>
                    </div>
                </div>
                <div class="footer-section">
                    <h4 class="footer-subtitle">Services</h4>
                    <ul class="footer-links">
                        <li><a href="financial-literacy.html">Financial Literacy</a></li>
                        <li><a href="fraud-protection.html">Fraud Protection</a></li>
                        <li><a href="resources.html">Investment Planning</a></li>
                        <li><a href="contact.html">Consultation</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 class="footer-subtitle">Office Locations</h4>
                    <div class="office-info">
                        <p><strong>Vancouver Office</strong><br>
                        2580 Granville Street, Suite 1200<br>
                        Vancouver, BC V6H 4B1</p>
                        <p><strong>Toronto Office</strong><br>
                        145 King Street West, Suite 2600<br>
                        Toronto, ON M5H 1J8</p>
                    </div>
                </div>
                <div class="footer-section">
                    <h4 class="footer-subtitle">Quick Links</h4>
                    <ul class="footer-links">
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="privacy.html">Privacy Policy</a></li>
                        <li><a href="contact.html">Contact</a></li>
                        <li><a href="resources.html">Resources</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Canadian Financial Security Group Inc. All rights reserved.</p>
                <p>Licensed financial services provider. PIPEDA compliant.</p>
            </div>
        </div>
    </footer>

    <script src="assets/js/main.js"></script>
</body>
</html>
