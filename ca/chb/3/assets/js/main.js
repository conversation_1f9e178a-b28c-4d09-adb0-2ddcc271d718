// Mobile Navigation Toggle
document.addEventListener('DOMContentLoaded', function() {
    const navToggle = document.querySelector('.nav-toggle');
    const navMenu = document.querySelector('.nav-menu');
    
    if (navToggle && navMenu) {
        navToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            navToggle.classList.toggle('active');
        });
    }
    
    // Close mobile menu when clicking on a link
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            navMenu.classList.remove('active');
            navToggle.classList.remove('active');
        });
    });
});

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Header scroll effect
window.addEventListener('scroll', function() {
    const header = document.querySelector('.header');
    if (window.scrollY > 100) {
        header.classList.add('scrolled');
    } else {
        header.classList.remove('scrolled');
    }
});

// Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('animate-in');
        }
    });
}, observerOptions);

// Observe elements for animation
document.querySelectorAll('.service-card, .testimonial-card, .stat-card').forEach(el => {
    observer.observe(el);
});

// Form validation (for contact forms)
function validateForm(form) {
    const inputs = form.querySelectorAll('input[required], textarea[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            isValid = false;
            input.classList.add('error');
        } else {
            input.classList.remove('error');
        }
        
        // Email validation
        if (input.type === 'email' && input.value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(input.value)) {
                isValid = false;
                input.classList.add('error');
            }
        }
        
        // Phone validation
        if (input.type === 'tel' && input.value) {
            const phoneRegex = /^[\+]?[1]?[\s\-\.]?[\(]?[0-9]{3}[\)]?[\s\-\.]?[0-9]{3}[\s\-\.]?[0-9]{4}$/;
            if (!phoneRegex.test(input.value)) {
                isValid = false;
                input.classList.add('error');
            }
        }
    });
    
    return isValid;
}

// Calculator functions
function calculateMortgage() {
    const homePrice = parseFloat(document.getElementById('principal').value);
    const downPaymentPercent = parseFloat(document.getElementById('down-payment').value) || 20;
    const rate = parseFloat(document.getElementById('rate').value) / 100 / 12;
    const years = parseFloat(document.getElementById('years').value);
    const payments = years * 12;

    if (homePrice && rate && years) {
        const downPayment = homePrice * (downPaymentPercent / 100);
        const principal = homePrice - downPayment;
        const monthlyPayment = principal * (rate * Math.pow(1 + rate, payments)) / (Math.pow(1 + rate, payments) - 1);
        const totalPayment = monthlyPayment * payments;
        const totalInterest = totalPayment - principal;

        document.getElementById('monthly-payment').textContent = formatCurrency(monthlyPayment);
        document.getElementById('total-payment').textContent = formatCurrency(totalPayment);
        document.getElementById('total-interest').textContent = formatCurrency(totalInterest);
        document.getElementById('loan-amount').textContent = formatCurrency(principal);
    }
}

function calculateRRSP() {
    const monthlyContribution = parseFloat(document.getElementById('monthly-contribution').value);
    const annualReturn = parseFloat(document.getElementById('annual-return').value) / 100 / 12;
    const years = parseFloat(document.getElementById('investment-years').value);
    const payments = years * 12;
    
    if (monthlyContribution && annualReturn && years) {
        const futureValue = monthlyContribution * ((Math.pow(1 + annualReturn, payments) - 1) / annualReturn);
        const totalContributions = monthlyContribution * payments;
        const totalGrowth = futureValue - totalContributions;
        
        document.getElementById('future-value').textContent = formatCurrency(futureValue);
        document.getElementById('total-contributions').textContent = formatCurrency(totalContributions);
        document.getElementById('total-growth').textContent = formatCurrency(totalGrowth);
    }
}

function calculateBudget() {
    const income = parseFloat(document.getElementById('monthly-income').value);
    const housing = parseFloat(document.getElementById('housing').value) || 0;
    const transportation = parseFloat(document.getElementById('transportation').value) || 0;
    const food = parseFloat(document.getElementById('food').value) || 0;
    const utilities = parseFloat(document.getElementById('utilities').value) || 0;
    const entertainment = parseFloat(document.getElementById('entertainment').value) || 0;
    const other = parseFloat(document.getElementById('other').value) || 0;
    
    if (income) {
        const totalExpenses = housing + transportation + food + utilities + entertainment + other;
        const remainingIncome = income - totalExpenses;
        const savingsRate = (remainingIncome / income) * 100;
        
        document.getElementById('total-expenses').textContent = formatCurrency(totalExpenses);
        document.getElementById('remaining-income').textContent = formatCurrency(remainingIncome);
        document.getElementById('savings-rate').textContent = savingsRate.toFixed(1) + '%';
        
        // Recommendations
        const recommendations = [];
        if (housing / income > 0.3) {
            recommendations.push('Consider reducing housing costs - aim for 25-30% of income');
        }
        if (transportation / income > 0.15) {
            recommendations.push('Transportation costs are high - consider alternatives');
        }
        if (savingsRate < 20) {
            recommendations.push('Try to save at least 20% of your income');
        }
        if (remainingIncome < 0) {
            recommendations.push('Your expenses exceed your income - immediate budget review needed');
        }
        
        const recommendationsEl = document.getElementById('budget-recommendations');
        if (recommendationsEl) {
            recommendationsEl.innerHTML = recommendations.map(rec => `<li>${rec}</li>`).join('');
        }
    }
}

// Utility functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-CA', {
        style: 'currency',
        currency: 'CAD'
    }).format(amount);
}

function formatPhoneNumber(phone) {
    const cleaned = phone.replace(/\D/g, '');
    const match = cleaned.match(/^(\d{1})(\d{3})(\d{3})(\d{4})$/);
    if (match) {
        return `+${match[1]} (${match[2]}) ${match[3]}-${match[4]}`;
    }
    return phone;
}

// Newsletter signup
function subscribeNewsletter(email) {
    if (validateEmail(email)) {
        // Simulate API call
        console.log('Newsletter subscription for:', email);
        alert('Thank you for subscribing to our newsletter!');
        return true;
    }
    return false;
}

function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Contact form submission
function submitContactForm(formData) {
    // Simulate form submission
    console.log('Contact form submitted:', formData);
    alert('Thank you for your message. We will contact you within 24 hours.');
}

// Appointment booking
function bookAppointment(appointmentData) {
    // Simulate appointment booking
    console.log('Appointment booked:', appointmentData);
    alert('Your appointment has been scheduled. You will receive a confirmation email shortly.');
}

// FAQ toggle functionality
document.querySelectorAll('.faq-question').forEach(question => {
    question.addEventListener('click', function() {
        const answer = this.nextElementSibling;
        const isOpen = answer.style.display === 'block';
        
        // Close all other answers
        document.querySelectorAll('.faq-answer').forEach(ans => {
            ans.style.display = 'none';
        });
        
        // Toggle current answer
        answer.style.display = isOpen ? 'none' : 'block';
        
        // Update question styling
        document.querySelectorAll('.faq-question').forEach(q => {
            q.classList.remove('active');
        });
        
        if (!isOpen) {
            this.classList.add('active');
        }
    });
});

// Lazy loading for images
if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });

    document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
    });
}

// Print functionality
function printPage() {
    window.print();
}

// Share functionality
function shareContent(title, url) {
    if (navigator.share) {
        navigator.share({
            title: title,
            url: url
        });
    } else {
        // Fallback for browsers that don't support Web Share API
        navigator.clipboard.writeText(url).then(() => {
            alert('Link copied to clipboard!');
        });
    }
}
