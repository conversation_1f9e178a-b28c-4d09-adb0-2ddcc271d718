/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-navy: #1E3A5F;
    --accent-gold: #16A085;
    --secondary-green: #27AE60;
    --white: #FFFFFF;
    --light-gray: #F0F8F5;
    --dark-gray: #2C3E50;
    --text-gray: #5A6C7D;
    --border-gray: #D5E8E0;
    
    --font-heading: 'Montserrat', sans-serif;
    --font-body: 'Inter', sans-serif;
    
    --shadow-soft: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
    --shadow-medium: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-large: 0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

body {
    font-family: var(--font-body);
    line-height: 1.6;
    color: var(--dark-gray);
    background-color: var(--white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: var(--white);
    box-shadow: var(--shadow-soft);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.nav {
    padding: 1rem 0;
}

.nav .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-link {
    display: flex;
    align-items: center;
    gap: 12px;
    text-decoration: none;
    color: inherit;
}

.logo {
    background: linear-gradient(135deg, #1e3a8a, #059669);
    color: white;
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: 700;
    font-size: 18px;
    letter-spacing: 1px;
}

.brand-text {
    font-family: var(--font-heading);
    font-weight: 700;
    font-size: 1.25rem;
    color: var(--primary-navy);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--dark-gray);
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-navy);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--accent-gold);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--primary-navy);
    transition: 0.3s;
}

/* Hero Section */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    margin-top: 80px;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -2;
}

.hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(27, 54, 93, 0.8) 0%, rgba(27, 54, 93, 0.6) 100%);
    z-index: -1;
}

.hero-content {
    color: var(--white);
    max-width: 600px;
    animation: slideUp 1s ease-out;
}

.hero-title {
    font-family: var(--font-heading);
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 1.5rem;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2.5rem;
    opacity: 0.95;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.btn {
    display: inline-block;
    padding: 1rem 2rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
}

.btn-primary {
    background: var(--accent-gold);
    color: var(--white);
    border-color: var(--accent-gold);
}

.btn-primary:hover {
    background: transparent;
    color: var(--accent-gold);
    border-color: var(--accent-gold);
}

.btn-secondary {
    background: transparent;
    color: var(--white);
    border-color: var(--white);
}

.btn-secondary:hover {
    background: var(--white);
    color: var(--primary-navy);
}

.hero-stats {
    display: flex;
    gap: 3rem;
    flex-wrap: wrap;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-family: var(--font-heading);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--accent-gold);
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Section Styles */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-family: var(--font-heading);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-navy);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--text-gray);
    max-width: 600px;
    margin: 0 auto;
}

/* Services Overview */
.services-overview {
    padding: 6rem 0;
    background: var(--light-gray);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.service-card {
    background: var(--white);
    padding: 2.5rem;
    border-radius: 12px;
    box-shadow: var(--shadow-soft);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    text-align: center;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.service-icon {
    margin-bottom: 1.5rem;
}

.service-icon img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
}

.service-title {
    font-family: var(--font-heading);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-navy);
    margin-bottom: 1rem;
}

.service-description {
    color: var(--text-gray);
    margin-bottom: 1.5rem;
    line-height: 1.7;
}

.service-link {
    color: var(--accent-gold);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.service-link:hover {
    color: var(--primary-navy);
}

/* Statistics Section */
.statistics {
    padding: 6rem 0;
    background: var(--primary-navy);
    color: var(--white);
}

.statistics .section-title {
    color: var(--white);
}

.statistics .section-subtitle {
    color: rgba(255, 255, 255, 0.8);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.stat-card {
    text-align: center;
    padding: 2rem;
}

.stat-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.stat-title {
    font-family: var(--font-heading);
    font-size: 2rem;
    font-weight: 700;
    color: var(--accent-gold);
    margin-bottom: 0.5rem;
}

.stat-description {
    opacity: 0.9;
}

/* Testimonials */
.testimonials {
    padding: 6rem 0;
    background: var(--white);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.testimonial-card {
    background: var(--light-gray);
    padding: 2.5rem;
    border-radius: 12px;
    border-left: 4px solid var(--accent-gold);
}

.testimonial-content {
    margin-bottom: 2rem;
}

.testimonial-content p {
    font-style: italic;
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--dark-gray);
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-photo {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.author-name {
    font-weight: 600;
    color: var(--primary-navy);
    margin-bottom: 0.25rem;
}

.author-title {
    color: var(--text-gray);
    font-size: 0.9rem;
}

/* CTA Section */
.cta {
    padding: 6rem 0;
    background: linear-gradient(135deg, var(--primary-navy) 0%, #2a4a73 100%);
    color: var(--white);
    text-align: center;
}

.cta-title {
    font-family: var(--font-heading);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.cta-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2.5rem;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Footer */
.footer {
    background: var(--dark-gray);
    color: var(--white);
    padding: 4rem 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-title {
    font-family: var(--font-heading);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--accent-gold);
}

.footer-subtitle {
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--white);
}

.footer-description {
    margin-bottom: 1.5rem;
    opacity: 0.8;
    line-height: 1.6;
}

.contact-info p {
    margin-bottom: 0.5rem;
    opacity: 0.9;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--accent-gold);
}

.office-info p {
    margin-bottom: 1rem;
    opacity: 0.8;
    line-height: 1.6;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 2rem;
    text-align: center;
    opacity: 0.7;
}

.footer-bottom p {
    margin-bottom: 0.5rem;
}

/* Animations */
@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Page Hero Styles */
.page-hero {
    position: relative;
    background: linear-gradient(135deg, var(--primary-navy) 0%, #2a4a73 100%);
    color: var(--white);
    padding: 8rem 0 4rem;
    margin-top: 80px;
    text-align: center;
    overflow: hidden;
}

.page-hero .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.page-hero .hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.3;
}

.page-hero .container {
    position: relative;
    z-index: 2;
}

.page-title {
    font-family: var(--font-heading);
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.page-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* Company Overview */
.company-overview {
    padding: 6rem 0;
    background: var(--white);
}

.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.content-text h2 {
    margin-bottom: 2rem;
}

.content-text p {
    margin-bottom: 1.5rem;
    line-height: 1.7;
    color: var(--text-gray);
}

.mission-points {
    margin-top: 2rem;
}

.mission-point {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--light-gray);
    border-radius: 8px;
}

.mission-point h4 {
    color: var(--primary-navy);
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.mission-point p {
    margin: 0;
    font-size: 0.95rem;
}

.about-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 12px;
    box-shadow: var(--shadow-medium);
}

/* Team Section */
.team-section {
    padding: 6rem 0;
    background: var(--light-gray);
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 3rem;
}

.team-member {
    background: var(--white);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-soft);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.team-member:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.member-photo {
    height: 250px;
    overflow: hidden;
}

.member-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.member-info {
    padding: 2rem;
}

.member-name {
    font-family: var(--font-heading);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-navy);
    margin-bottom: 0.5rem;
}

.member-title {
    color: var(--accent-gold);
    font-weight: 600;
    margin-bottom: 1rem;
}

.member-bio {
    color: var(--text-gray);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.member-credentials {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.credential {
    background: var(--primary-navy);
    color: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Company Values */
.company-values {
    padding: 6rem 0;
    background: var(--white);
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.value-card {
    text-align: center;
    padding: 2rem;
    border-radius: 12px;
    background: var(--light-gray);
    transition: transform 0.3s ease;
}

.value-card:hover {
    transform: translateY(-5px);
}

.value-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.value-title {
    font-family: var(--font-heading);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-navy);
    margin-bottom: 1rem;
}

.value-description {
    color: var(--text-gray);
    line-height: 1.6;
}

/* Certifications */
.certifications {
    padding: 6rem 0;
    background: var(--primary-navy);
    color: var(--white);
}

.certifications .section-title {
    color: var(--white);
}

.certifications .section-subtitle {
    color: rgba(255, 255, 255, 0.8);
}

.certifications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.cert-item {
    text-align: center;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

.cert-item h4 {
    color: var(--accent-gold);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.cert-item p {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Office Locations */
.office-locations {
    padding: 6rem 0;
    background: var(--light-gray);
}

.offices-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 3rem;
}

.office-card {
    background: var(--white);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-soft);
}

.office-image {
    height: 200px;
    overflow: hidden;
}

.office-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.office-info {
    padding: 2rem;
}

.office-title {
    font-family: var(--font-heading);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-navy);
    margin-bottom: 1.5rem;
}

.office-address {
    margin-bottom: 2rem;
}

.office-address p {
    margin-bottom: 0.5rem;
    color: var(--text-gray);
}

.office-hours h4 {
    color: var(--primary-navy);
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

.office-hours p {
    margin-bottom: 0.25rem;
    color: var(--text-gray);
    font-size: 0.9rem;
}

/* Financial Literacy Page Styles */
.services-detailed-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 3rem;
}

.service-detailed-card {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    background: var(--white);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-soft);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-detailed-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.service-detailed-card:nth-child(even) {
    direction: rtl;
}

.service-detailed-card:nth-child(even) .service-content {
    direction: ltr;
}

.service-image {
    height: 300px;
    overflow: hidden;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.service-content {
    padding: 2rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.service-features {
    list-style: none;
    margin: 1.5rem 0;
}

.service-features li {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-gray);
    color: var(--text-gray);
    position: relative;
    padding-left: 1.5rem;
}

.service-features li:before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--accent-gold);
    font-weight: bold;
}

.service-pricing {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 2px solid var(--border-gray);
}

.price {
    font-family: var(--font-heading);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-navy);
}

/* Education Programs */
.education-programs {
    padding: 6rem 0;
    background: var(--light-gray);
}

.programs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.program-card {
    background: var(--white);
    border-radius: 12px;
    padding: 2.5rem;
    box-shadow: var(--shadow-soft);
    transition: transform 0.3s ease;
    position: relative;
}

.program-card.featured {
    border: 3px solid var(--accent-gold);
    transform: scale(1.05);
}

.program-card:hover {
    transform: translateY(-5px);
}

.program-card.featured:hover {
    transform: scale(1.05) translateY(-5px);
}

.program-level {
    display: inline-block;
    background: var(--primary-navy);
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.program-card.featured .program-level {
    background: var(--accent-gold);
}

.program-title {
    font-family: var(--font-heading);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-navy);
    margin-bottom: 1rem;
}

.program-description {
    color: var(--text-gray);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.program-modules {
    list-style: none;
    margin-bottom: 2rem;
}

.program-modules li {
    padding: 0.5rem 0;
    color: var(--text-gray);
    position: relative;
    padding-left: 1.5rem;
}

.program-modules li:before {
    content: '📚';
    position: absolute;
    left: 0;
}

.program-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--light-gray);
    border-radius: 8px;
}

.program-duration {
    color: var(--text-gray);
    font-weight: 500;
}

.program-price {
    font-family: var(--font-heading);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-navy);
}

/* Canadian Focus Section */
.canadian-focus {
    padding: 6rem 0;
    background: var(--white);
}

.canadian-topics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.topic-card {
    background: var(--light-gray);
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    transition: transform 0.3s ease;
}

.topic-card:hover {
    transform: translateY(-5px);
}

.topic-title {
    font-family: var(--font-heading);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-navy);
    margin-bottom: 1rem;
}

.topic-description {
    color: var(--text-gray);
    line-height: 1.6;
}

/* Success Stories */
.success-stories {
    padding: 6rem 0;
    background: var(--primary-navy);
    color: var(--white);
}

.success-stories .section-title {
    color: var(--white);
}

.success-stories .section-subtitle {
    color: rgba(255, 255, 255, 0.8);
}

.success-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.success-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 2.5rem;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    text-align: center;
}

.success-stats {
    margin-bottom: 1.5rem;
}

.success-number {
    display: block;
    font-family: var(--font-heading);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--accent-gold);
    margin-bottom: 0.5rem;
}

.success-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.success-story {
    font-style: italic;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    opacity: 0.95;
}

.success-author {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: 1rem;
}

.success-author strong {
    display: block;
    margin-bottom: 0.25rem;
}

.success-author span {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Fraud Protection Page Styles */
.fraud-stats {
    padding: 6rem 0;
    background: var(--light-gray);
}

.fraud-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.fraud-stat-card {
    background: var(--white);
    padding: 2.5rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: var(--shadow-soft);
    border-left: 4px solid var(--accent-gold);
}

.fraud-stat-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.fraud-stat-number {
    font-family: var(--font-heading);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-navy);
    margin-bottom: 0.5rem;
}

.fraud-stat-label {
    font-weight: 600;
    color: var(--dark-gray);
    margin-bottom: 0.5rem;
}

.fraud-stat-detail {
    font-size: 0.9rem;
    color: var(--text-gray);
    font-style: italic;
}

.protection-services {
    padding: 6rem 0;
    background: var(--white);
}

/* Common Scams Section */
.common-scams {
    padding: 6rem 0;
    background: var(--light-gray);
}

.scams-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.scam-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: var(--shadow-soft);
    transition: transform 0.3s ease;
}

.scam-card:hover {
    transform: translateY(-5px);
}

.scam-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.scam-title {
    font-family: var(--font-heading);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-navy);
    margin-bottom: 1rem;
}

.scam-description {
    color: var(--text-gray);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.scam-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 1rem;
    color: #856404;
}

.scam-warning strong {
    color: #533f03;
}

/* Protection Checklist */
.protection-checklist {
    padding: 6rem 0;
    background: var(--white);
}

.checklist-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.checklist-category {
    background: var(--light-gray);
    padding: 2rem;
    border-radius: 12px;
}

.checklist-title {
    font-family: var(--font-heading);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-navy);
    margin-bottom: 1.5rem;
}

.checklist-items {
    list-style: none;
}

.checklist-items li {
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-gray);
    color: var(--text-gray);
    position: relative;
    padding-left: 2rem;
}

.checklist-items li:before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--accent-gold);
    font-weight: bold;
    font-size: 1.2rem;
}

.checklist-items li:last-child {
    border-bottom: none;
}

/* Emergency Response */
.emergency-response {
    padding: 6rem 0;
    background: var(--primary-navy);
    color: var(--white);
}

.emergency-response .section-title {
    color: var(--white);
}

.emergency-response .section-subtitle {
    color: rgba(255, 255, 255, 0.8);
}

.emergency-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.emergency-step {
    display: flex;
    gap: 1.5rem;
    align-items: flex-start;
}

.step-number {
    background: var(--accent-gold);
    color: var(--primary-navy);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: var(--font-heading);
    font-weight: 700;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.step-content {
    flex: 1;
}

.step-title {
    font-family: var(--font-heading);
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--white);
}

.step-description {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    margin-bottom: 1rem;
}

/* Resources Page Styles */
.calculators-section {
    padding: 6rem 0;
    background: var(--light-gray);
}

.calculator-card {
    background: var(--white);
    border-radius: 12px;
    box-shadow: var(--shadow-soft);
    margin-bottom: 3rem;
    overflow: hidden;
}

.calculator-header {
    background: var(--primary-navy);
    color: var(--white);
    padding: 2rem;
    text-align: center;
}

.calculator-title {
    font-family: var(--font-heading);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.calculator-description {
    opacity: 0.9;
}

.calculator-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    padding: 2rem;
}

.calculator-inputs {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.input-group {
    display: flex;
    flex-direction: column;
}

.input-group label {
    font-weight: 600;
    color: var(--primary-navy);
    margin-bottom: 0.5rem;
}

.input-group input {
    padding: 0.75rem;
    border: 2px solid var(--border-gray);
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.input-group input:focus {
    outline: none;
    border-color: var(--accent-gold);
}

.calculator-results {
    background: var(--light-gray);
    padding: 1.5rem;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-gray);
}

.result-item:last-child {
    border-bottom: none;
}

.result-label {
    font-weight: 500;
    color: var(--text-gray);
}

.result-value {
    font-family: var(--font-heading);
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--primary-navy);
}

.budget-recommendations {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 2px solid var(--border-gray);
}

.budget-recommendations h4 {
    color: var(--primary-navy);
    margin-bottom: 0.75rem;
}

.budget-recommendations ul {
    list-style: none;
}

.budget-recommendations li {
    padding: 0.5rem 0;
    color: var(--text-gray);
    position: relative;
    padding-left: 1.5rem;
}

.budget-recommendations li:before {
    content: '💡';
    position: absolute;
    left: 0;
}

/* Educational Resources */
.educational-resources {
    padding: 6rem 0;
    background: var(--white);
}

.resources-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.resource-card {
    background: var(--light-gray);
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.resource-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.resource-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.resource-title {
    font-family: var(--font-heading);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-navy);
    margin-bottom: 1rem;
}

.resource-description {
    color: var(--text-gray);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.resource-link {
    color: var(--accent-gold);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.resource-link:hover {
    color: var(--primary-navy);
}

/* Canadian Resources */
.canadian-resources {
    padding: 6rem 0;
    background: var(--light-gray);
}

.official-links-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.official-link-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: var(--shadow-soft);
}

.official-title {
    font-family: var(--font-heading);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-navy);
    margin-bottom: 1.5rem;
}

.official-links {
    list-style: none;
}

.official-links li {
    margin-bottom: 0.75rem;
}

.official-links a {
    color: var(--text-gray);
    text-decoration: none;
    transition: color 0.3s ease;
    display: block;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-gray);
}

.official-links a:hover {
    color: var(--accent-gold);
}

/* Newsletter Signup */
.newsletter-signup {
    padding: 6rem 0;
    background: var(--primary-navy);
    color: var(--white);
    text-align: center;
}

.newsletter-title {
    font-family: var(--font-heading);
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.newsletter-description {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.newsletter-form {
    max-width: 500px;
    margin: 0 auto;
}

.form-group {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group input {
    flex: 1;
    padding: 1rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
}

.form-group input:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(201, 169, 97, 0.3);
}

.newsletter-privacy {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Contact Page Styles */
.contact-section {
    padding: 6rem 0;
    background: var(--white);
}

.contact-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
}

.contact-form-section {
    background: var(--light-gray);
    padding: 3rem;
    border-radius: 12px;
}

.contact-form {
    margin-top: 2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    margin-bottom: 1.5rem;
}

.form-group label {
    font-weight: 600;
    color: var(--primary-navy);
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem;
    border: 2px solid var(--border-gray);
    border-radius: 8px;
    font-size: 1rem;
    font-family: var(--font-body);
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent-gold);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.checkbox-group {
    flex-direction: row;
    align-items: flex-start;
    gap: 0.75rem;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    cursor: pointer;
    font-size: 0.9rem;
    line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
    width: auto;
}

.checkbox-label a {
    color: var(--accent-gold);
    text-decoration: none;
}

.checkbox-label a:hover {
    text-decoration: underline;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    width: 100%;
}

.contact-info-section {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-method {
    display: flex;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--light-gray);
    border-radius: 8px;
}

.contact-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.contact-details h3 {
    font-family: var(--font-heading);
    font-weight: 600;
    color: var(--primary-navy);
    margin-bottom: 0.75rem;
}

.contact-details p {
    margin-bottom: 0.5rem;
    color: var(--text-gray);
}

.contact-details strong {
    color: var(--dark-gray);
}

.contact-details em {
    color: var(--accent-gold);
    font-style: normal;
    font-weight: 500;
}

/* Appointment Booking */
.appointment-booking {
    padding: 6rem 0;
    background: var(--light-gray);
}

.booking-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.booking-option {
    background: var(--white);
    padding: 2.5rem;
    border-radius: 12px;
    box-shadow: var(--shadow-soft);
    text-align: center;
    transition: transform 0.3s ease;
}

.booking-option.featured {
    border: 3px solid var(--accent-gold);
    transform: scale(1.05);
}

.booking-option:hover {
    transform: translateY(-5px);
}

.booking-option.featured:hover {
    transform: scale(1.05) translateY(-5px);
}

.booking-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.booking-title {
    font-family: var(--font-heading);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-navy);
    margin-bottom: 1rem;
}

.booking-description {
    color: var(--text-gray);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.booking-features {
    list-style: none;
    text-align: left;
    margin-bottom: 2rem;
}

.booking-features li {
    padding: 0.5rem 0;
    color: var(--text-gray);
    position: relative;
    padding-left: 1.5rem;
}

.booking-features li:before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--accent-gold);
    font-weight: bold;
}

.booking-pricing {
    margin-bottom: 2rem;
    padding: 1rem;
    background: var(--light-gray);
    border-radius: 8px;
}

.booking-price {
    font-family: var(--font-heading);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-navy);
}

/* FAQ Section */
.faq-section {
    padding: 6rem 0;
    background: var(--white);
}

.faq-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    border: 1px solid var(--border-gray);
    border-radius: 8px;
    overflow: hidden;
}

.faq-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: var(--light-gray);
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.faq-question:hover {
    background: #e9ecef;
}

.faq-question h3 {
    font-family: var(--font-heading);
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-navy);
    margin: 0;
}

.faq-toggle {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--accent-gold);
    transition: transform 0.3s ease;
}

.faq-question.active .faq-toggle {
    transform: rotate(45deg);
}

.faq-answer {
    padding: 1.5rem;
    background: var(--white);
    display: none;
}

.faq-answer p {
    color: var(--text-gray);
    line-height: 1.6;
    margin: 0;
}

/* Privacy Policy Styles */
.policy-date {
    font-style: italic;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 1rem;
}

.privacy-content {
    padding: 6rem 0;
    background: var(--white);
}

.privacy-document {
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.7;
}

.privacy-section {
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--border-gray);
}

.privacy-section:last-child {
    border-bottom: none;
}

.privacy-section h2 {
    font-family: var(--font-heading);
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--primary-navy);
    margin-bottom: 1.5rem;
}

.privacy-section h3 {
    font-family: var(--font-heading);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-navy);
    margin: 2rem 0 1rem 0;
}

.privacy-section p {
    color: var(--text-gray);
    margin-bottom: 1rem;
}

.privacy-section ul {
    margin: 1rem 0 1rem 2rem;
}

.privacy-section li {
    color: var(--text-gray);
    margin-bottom: 0.5rem;
}

.privacy-section strong {
    color: var(--dark-gray);
    font-weight: 600;
}

.contact-details {
    background: var(--light-gray);
    padding: 1.5rem;
    border-radius: 8px;
    margin: 1rem 0;
}

.contact-details p {
    margin-bottom: 0.5rem;
}

.privacy-footer {
    background: var(--light-gray);
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    margin-top: 3rem;
}

.privacy-footer p {
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--primary-navy);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 80px;
        left: 0;
        width: 100%;
        height: calc(100vh - 80px);
        background: var(--white);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding: 2rem 0;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        z-index: 1000;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .nav-menu.active {
        transform: translateX(0);
    }

    .nav-menu li {
        margin: 1rem 0;
    }

    .nav-link {
        font-size: 1.2rem;
        color: var(--primary-navy);
        padding: 1rem 2rem;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .nav-link:hover,
    .nav-link.active {
        background: var(--primary-navy);
        color: var(--white);
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .nav-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .hero-stats {
        gap: 2rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .services-grid,
    .testimonials-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .page-title {
        font-size: 2.5rem;
    }

    .content-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .team-grid {
        grid-template-columns: 1fr;
    }

    .values-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .certifications-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .offices-grid {
        grid-template-columns: 1fr;
    }

    .services-detailed-grid {
        grid-template-columns: 1fr;
    }

    .programs-grid {
        grid-template-columns: 1fr;
    }

    .canadian-topics-grid {
        grid-template-columns: 1fr;
    }

    .success-grid {
        grid-template-columns: 1fr;
    }

    .fraud-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .scams-grid {
        grid-template-columns: 1fr;
    }

    .checklist-grid {
        grid-template-columns: 1fr;
    }

    .emergency-steps {
        grid-template-columns: 1fr;
    }

    .emergency-step {
        flex-direction: column;
        text-align: center;
    }

    .calculator-content {
        grid-template-columns: 1fr;
    }

    .resources-grid {
        grid-template-columns: 1fr;
    }

    .official-links-grid {
        grid-template-columns: 1fr;
    }

    .form-group {
        flex-direction: column;
    }

    .contact-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .booking-options {
        grid-template-columns: 1fr;
    }

    .booking-option.featured {
        transform: none;
    }

    .booking-option.featured:hover {
        transform: translateY(-5px);
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .service-card,
    .testimonial-card {
        padding: 1.5rem;
    }
}
